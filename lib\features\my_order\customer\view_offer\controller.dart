import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/my_order/customer/OrderPending/controller.dart';
import 'package:renvo_app/features/my_order/customer/OrderUnderway/controller.dart';
import 'package:renvo_app/features/my_order/models/my_offer.dart';
import 'package:renvo_app/features/my_order/models/view_offer.dart';

class ViewOffersPageController extends GetxController {
  // ObsList<VOfferModel> offers = ObsList([]);
  late PaginationController pagerController;
  final pending_controller = Get.put(OrderPendingController());
  final progress_controller = Get.put(OrderProcessingController());
  @override
  void onInit() {
    super.onInit();
  }

  Future<ResponseModel> Function(int, dio.CancelToken) viewOffer(int id) {
    return (int page, dio.CancelToken cancel) async {
      final response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.order_offers(id),
          params: {
            "page": page,
          },
          cancelToken: cancel,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
        ),
      );
      return response;
    };
  }

  refreshData() {
    pagerController.refreshData();
  }

  Future<void> acceptOffer(int offerId) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.accept_offers,
        method: RequestMethod.Post,
        body: dio.FormData.fromMap({
          "offer_id": offerId,
        }),
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );

    if (response.success) {
      pending_controller.pagerController.refreshData();

      progress_controller.pagerController.refreshData();

      Get.snackbar("Success", "Offer accepted successfully");
    } else {
      Get.snackbar("Error", "Failed to accept offer");
    }
    Get.back();
    // return response;
  }

  @override
  void onClose() {
    super.onClose();
  }
}
