import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/config/role.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController number, password, confirm_password;

  @override
  onInit() {
    number = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    number.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    Future<void> _makeRegistrationAPICall() async {
      // String cleanPhone = phoneController.text;

      Map<String, dynamic> jsonData = {
        'phone': number.text,
        'dial_country_code': '963',
        'password': password.text,
        'password_confirmation': confirm_password.text,
      };

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.register,
          method: RequestMethod.Post,
          body: jsonData,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
        ),
      );

        if (response.success) {
          await _handleSuccessfulRegistration(response, number.text);
        } else {
          // _handleRegistrationError(response);
        }
      }

      Future<void> _handleSuccessfulRegistration(
        ResponseModel response,
        String cleanPhone,
        // String dialCode,
      ) async {
        final appBuilder = Get.find<AppBuilder>();

        appBuilder.setRole(Role.new_user);
        appBuilder.setToken(null);
        // appBuilder.setVerified(false);
        // appBuilder.setProfileCompleted(false);

        // Extract success message (same logic as original)
        String successMessage =
            'Registration successful! Please verify your phone.';
        try {
          if (response.data is List && (response.data as List).isNotEmpty) {
            successMessage = (response.data as List).first.toString();
          } else if (response.data is Map<String, dynamic>) {
            final responseData = response.data as Map<String, dynamic>;
            if (responseData['message'] != null) {
              successMessage = responseData['message'].toString();
            }
          }
        } catch (e) {
          print(e);
        }
        // ResponseModel response = await APIService.instance.request(
        //   Request(
        //     endPoint: EndPoints.register,
        //     method: RequestMethod.Post,
        //     // params: {"success": true},
        //     body: {
        //       "number": number.text,
        //       "password": password.text,
        //     },
        //   ),
        // );
        // if (response.success) {
        //   Get.toNamed(Pages.home.value);
        // } else {}
      }
    }
  }

