import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/config/role.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';

class RegisterPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController number, password, confirmPassword;

  @override
  onInit() {
    number = TextEditingController();
    password = TextEditingController();
    confirmPassword = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    number.dispose();
    password.dispose();
    confirmPassword.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    await _makeRegistrationAPICall();
  }

  Future<void> _makeRegistrationAPICall() async {
    Map<String, dynamic> jsonData = {
      'phone': number.text,
      'dial_country_code': '963',
      'password': password.text,
      'password_confirmation': confirmPassword.text,
    };

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        body: jsonData,
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );

    if (response.success) {
      await _handleSuccessfulRegistration(response, number.text);
    } else {
      // _handleRegistrationError(response);
    }
  }

  Future<void> _handleSuccessfulRegistration(
    ResponseModel response,
    String cleanPhone,
  ) async {
    final appBuilder = Get.find<AppBuilder>();

    appBuilder.setRole(Role.new_user);
    appBuilder.setToken(null);

    // Extract success message
    String successMessage =
        'Registration successful! Please verify your phone.';
    try {
      if (response.data is List && (response.data as List).isNotEmpty) {
        successMessage = (response.data as List).first.toString();
      } else if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData['message'] != null) {
          successMessage = responseData['message'].toString();
        }
      }
    } catch (e) {
      print('Error extracting success message: $e');
    }

    // Show success message or navigate to next screen
    Get.snackbar('Success', successMessage);
  }
}
