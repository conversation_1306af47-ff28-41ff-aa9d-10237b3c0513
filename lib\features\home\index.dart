// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:renvo_app/core/demo/media.dart';
// import 'package:renvo_app/core/routes/routes.dart';
// import 'package:renvo_app/core/style/repo.dart';
// import 'package:renvo_app/core/style/style.dart';
// import 'package:renvo_app/core/widgets/image.dart';
// import 'package:renvo_app/core/widgets/svg_icon.dart';
// import 'package:renvo_app/features/home/<USER>/user_home.dart';
// import 'package:renvo_app/gen/assets.gen.dart';

// import 'controller.dart';

// class HomePage extends StatelessWidget {
//   const HomePage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final HomePageController controller = Get.put(HomePageController());
//     Get.put(HomePageController());
//     return Scaffold(
//         backgroundColor: StyleRepo.deepBlue1,
//         // backgroundColor: StyleRepo.deepBlue,
//         body: Stack(
//           children: [
//             // SizedBox(
//             //   height: double.infinity,
//             //   width: double.infinity,
//             // child:
//             Container(
//                 height: double.infinity,
//                 child: AppImage(
//                   path: Assets.images.backgroundImg.path,
//                   type: ImageType.Asset,
//                   fit: BoxFit.fill,
//                 )),
//             // )
//             // Positioned(
//             //   left: 30,
//             //   top: MediaQuery.sizeOf(context).height * 0.30,
//             //   child: SizedBox(
//             //     child: Icon(
//             //       Icons.add_ic_call_outlined,
//             //       opticalSize: 70,
//             //     ),
//             //   ),
//             // ),

//             Positioned(
//               bottom: 0,
//               left: 0,
//               right: 0,
//               top: MediaQuery.sizeOf(context).height * 0.30,
//               child: Container(
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(30),
//                         topRight: Radius.circular(30)),
//                     color: StyleRepo.white,
//                   ),
//                   // height: MediaQuery.sizeOf(context).height * 0.50,
//                   child: UserHomeWidget(controller: controller)),
//             )
//           ],
//         ));
//   }
// }

import 'package:renvo_app/features/home/<USER>/provider_home.dart';
import 'package:renvo_app/features/home/<USER>/user_home.dart';

import 'controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/demo/media.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/style/style.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/gen/assets.gen.dart';
import 'package:renvo_app/core/config/app_builder.dart';
// باقي import كما في كودك

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final HomePageController controller = Get.put(HomePageController());
    final appBuilder = Get.find<AppBuilder>();

    return Scaffold(
      backgroundColor: StyleRepo.deepBlueFegma,
      // backgroundColor: StyleRepo.deepBlueFegma,
      body: Builder(builder: (context) {
        return Obx(() {
          // الشرط هنا حسب وضع التطبيق الحالي
          if (appBuilder.isProviderMode.value) {
            return ProviderHomeWidget();
          } else {
            return UserHomeWidget(controller: controller);
          }
        });
      }),
    );
  }
}
