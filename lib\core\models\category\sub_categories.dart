class SubCategory {
  final int id;
  final String title;
  final String? svg;
  final int minPrice;
  final int maxPrice;
  // final String? description;
  final int prvCnt;
  // final List<String> keywords;
  // late int id;
  // late String title;
  // late String? svg;
  // late int minPrice;
  // late int maxPrice;
  // late String? description;
  // late int prvCnt;
  // late List<String> keywords;

  SubCategory({
    required this.id,
    required this.title,
    this.svg,
    required this.minPrice,
    required this.maxPrice,
    // this.description,
    required this.prvCnt,
    // required this.keywords,
  });

  factory SubCategory.fromJson(Map<String, dynamic> json) => SubCategory(
        id: json['id'],
        title: json['title'],
        svg: json['svg'],
        minPrice: json['min_price'],
        maxPrice: json['max_price'],
        // description: json['description'],
        prvCnt: json['prv_cnt'],
        // keywords: (json['keywords'] as List)
        // .map((k) => k['title'].toString())
        // .toList(),
      );
  // SubCategory.fromJson(Map<String, dynamic> json) {
  //   id = json["id"];
  //   title = json["title"];
  //   description = json["description"];
  //   minPrice = json["min_price"];
  //   maxPrice = json["max_price"];
  //   prvCnt = json["prv_cnt"];
  //   svg = json["svg"];
  //   // image = json["image"] ?? "";
  //   // priceBeforeDiscount = json["price_before_discount"];
  //   keywords:
  //   (json['keywords'] as List).map((k) => k['title'].toString()).toList();
  // }
  // SubCategory.fromJson(Map<String, dynamic> json) {
  //   id = json["id"];
  //   title = json["title"];
  //   minPrice = json["min_price"];
  //   maxPrice = json["max_price"];
  //   prvCnt = json["prv_cnt"];
  //   svg = json["svg"];
  //   // description معالجة خاصة
  //   description = (json["description"] is List)
  //       ? (json["description"] as List).join(", ")
  //       : json["description"]?.toString();
  //   // keywords معالجة صحيحة
  //   keywords =
  //       (json['keywords'] as List).map((k) => k['title'].toString()).toList();
  // }
}
