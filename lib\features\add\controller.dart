// ResponseModel response = await APIService.instance.request(
//   Request(
//     endPoint: EndPoints.register,
//     method: RequestMethod.Post,

//     body: FormData.fromMap(
//       {
//         "name": email.text,
//         "phone": '',
//          "dial_country_code": '963',
//          "description":'hi',
//            "email":'hi',
//              "address_id":'hi',
//                "street_name":'hi',
//                  "post_code":'hi',
//                  "country_id":'hi',
//                  "city_id":'hi',
//                  "post_code":'hi',

//         // "image": await MultipartFile.fromFile(image.value),
//       },
//     ),
//   ),
// );
// if (response.success) {
// } else {}

// import 'package:get/get.dart';
// import 'package:renvo_app/core/services/rest_api/rest_api.dart';
// import 'package:renvo_app/features/join_provider/model/all_categories.dart';

// class ProviderCategoriesController extends GetxController {
//   RxList<ProviderCategory> categories = <ProviderCategory>[].obs;
//   RxBool isLoading = false.obs;
//   RxString error = ''.obs;

//   @override
//   void onInit() {
//     fetchCategories();
//     super.onInit();
//   }

//   Future<void> fetchCategories() async {
//     isLoading.value = true;
//     error.value = '';
//     final response = await APIService.instance.request(
//       Request(
//         endPoint: '/api/v1/provider_categories', // عدل الرابط حسب الحاجة
//         method: RequestMethod.Get,
//       ),
//     );
//     isLoading.value = false;

//     if (response.success && response.data != null) {
//       // تأكد أن الداتا قائمة
//       final List rawList = response.data;
//       categories.value = rawList.map((e) => ProviderCategory.fromJson(e)).toList();
//     } else {
//       error.value = response.message ?? "Failed to load categories";
//     }
//   }
// }
import 'package:get/get.dart';
import 'package:renvo_app/core/models/category/all_categories.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
// import 'package:renvo_app/features/join_provider/model/all_categories.dart';

class AddPageController extends GetxController {
  ObsList<AllCategory> categories = ObsList([]);

  @override
  void onInit() {
    fetchCategories();
    super.onInit();
  }

  Future<void> fetchCategories() async {
    final response = await APIService.instance.request(
      Request(endPoint: EndPoints.provider_categories, copyHeader: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }),
    );

    if (response.success && response.data != null) {
      final List rawList = response.data;
      categories.value = rawList.map((e) => AllCategory.fromJson(e)).toList();
    } else {
      print(response.message ?? "Failed to load categories");
    }
  }
}
