import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/add/models/order_models.dart';

class ServiceTypeController extends GetxController {
  RxInt selectedType = 0.obs; // 0 = ASAP, 1 = Specific Date
  Rx<DateTime?> selectedDate = Rx<DateTime?>(null);
  Rx<TimeOfDay?> selectedTime = Rx<TimeOfDay?>(null);

//  late final OrderDataModel order;
  // var selectedDate = Rxn<DateTime>(); // nullable date
  // var selectedTime = Rxn<TimeOfDay>();
  late final int id_cat;
  late final int id_subcat;

  // final args = Get.arguments as Map<String, dynamic>;
  String? get formattedDate => selectedDate.value != null
      ? "${selectedDate.value!.toLocal()}".split(' ')[0]
      : null;

  String? get formattedTime =>
      selectedTime.value != null ? _format24(selectedTime.value!) : null;

  static String _format24(TimeOfDay t) {
    final hours = t.hour.toString().padLeft(2, '0');
    final minutes = t.minute.toString().padLeft(2, '0');
    return "$hours:$minutes:00";
  }

  @override
  onInit() {
    // id_subcat = Get.arguments;
    // order = Get.arguments as OrderDataModel;

    // id_cat = args["categoryId"];
    // id_subcat = args["subCategoryId"];
    // log(id_cat.toString());
    // fetch();
    super.onInit();
  }
}





  // يمكنك إضافة دوال مساعدة إذا أردت


