// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:renvo_app/features/add/models/order_models.dart';

// import 'controller.dart'; // استورد الكنترولر هنا

// class AddOrderPage4 extends StatelessWidget {
//   // AddOrderPage4.AddOrderPage4({super.key});
//   const AddOrderPage4({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(AddOrderPage4Controller());
//     final OrderDataModel order = Get.arguments as OrderDataModel;

//     const colorMain = Color(0xFF5B20C7);

//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//           child: SingleChildScrollView(
//             child: Column(
//               children: [
//                 const SizedBox(height: 8),
//                 const Text(
//                   "Complete Order",
//                   style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
//                 ),
//                 const SizedBox(height: 28),
//                 Align(
//                   alignment: Alignment.centerLeft,
//                   child: Text("Description",
//                       style: TextStyle(fontWeight: FontWeight.w600)),
//                 ),
//                 const SizedBox(height: 8),
//                 Container(
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     borderRadius: BorderRadius.circular(14),
//                     border: Border.all(color: Colors.grey.shade300),
//                   ),
//                   child: TextField(
//                     controller: controller.descController,
//                     minLines: 4,
//                     maxLines: 8,
//                     decoration: const InputDecoration(
//                       hintText: "Add Description",
//                       border: InputBorder.none,
//                       contentPadding:
//                           EdgeInsets.symmetric(horizontal: 12, vertical: 12),
//                     ),
//                   ),
//                 ),
//                 const SizedBox(height: 22),
//                 Align(
//                   alignment: Alignment.centerLeft,
//                   child: Text("Upload Photos",
//                       style: TextStyle(fontWeight: FontWeight.w600)),
//                 ),
//                 const SizedBox(height: 8),
//                 Obx(() => GestureDetector(
//                       onTap: controller.pickImages,
//                       child: Container(
//                         width: double.infinity,
//                         height: 100,
//                         decoration: BoxDecoration(
//                           border: Border.all(
//                               color: Colors.grey.shade300, width: 1.1),
//                           borderRadius: BorderRadius.circular(14),
//                           color: Colors.grey[100],
//                         ),
//                         child: controller.images.isEmpty
//                             ? Column(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: const [
//                                   Icon(Icons.upload,
//                                       color: Color(0xFF5B20C7), size: 30),
//                                   SizedBox(height: 6),
//                                   Text("Upload Photo",
//                                       style: TextStyle(color: Colors.grey)),
//                                 ],
//                               )
//                             : ListView(
//                                 scrollDirection: Axis.horizontal,
//                                 children: controller.images
//                                     .map((img) => Padding(
//                                           padding: const EdgeInsets.all(8.0),
//                                           child: ClipRRect(
//                                             borderRadius:
//                                                 BorderRadius.circular(8),
//                                             child: Image.file(
//                                               File(img.path),
//                                               height: 80,
//                                               width: 80,
//                                               fit: BoxFit.cover,
//                                             ),
//                                           ),
//                                         ))
//                                     .toList(),
//                               ),
//                       ),
//                     )),
//                 const SizedBox(height: 38),
//                 SizedBox(
//                   width: double.infinity,
//                   height: 47,
//                   child: ElevatedButton(
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: colorMain,
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(25),
//                       ),
//                     ),
//                     onPressed: () {
//                       // تنفيذ رفع الطلب هنا
//                       // controller.descController.text, controller.images
//                       order.description = controller.descController.text;

//                       // حفظ الصور (مثلاً فقط المسارات/الروابط، أو إذا عندك رفع لاحق)
//                       order.images =
//                           controller.images.map((img) => img.path).toList();
//                       print(order.toJson());
//                       // مثلاً الانتقال لصفحة الملخص
//                       // Get.toNamed(Pages.order_summary.value, arguments: order);
//                     },
//                     child: const Text(
//                       "Done",
//                       style: TextStyle(
//                           fontSize: 16.5, fontWeight: FontWeight.bold),
//                     ),
//                   ),
//                 ),
//                 const SizedBox(height: 12),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'controller.dart';

class AddOrderPage4 extends StatelessWidget {
  const AddOrderPage4({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddOrderPage4Controller());
    final OrderDataModel order = Get.arguments as OrderDataModel;
    // const colorMain = Color(0xFF5B20C7);
    const colorMain = StyleRepo.deebBlue2;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 8),
                const Text(
                  "Complete Order",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                ),
                const SizedBox(height: 24),

                // ---- Price Range ----
                Obx(() => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Price Range",
                              style: TextStyle(fontWeight: FontWeight.w600),
                            ),
                            Text(
                              "${controller.priceRange.value.start.toInt()} - ${controller.priceRange.value.end.toInt()} SEK",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: colorMain,
                                fontSize: 13.5,
                              ),
                            ),
                          ],
                        ),
                        RangeSlider(
                          min: 100,
                          max: 400,
                          divisions: 3,
                          values: controller.priceRange.value,
                          onChanged: (range) =>
                              controller.priceRange.value = range,
                          activeColor: colorMain,
                          inactiveColor: colorMain.withOpacity(.17),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: const [
                            Text("100 SEK", style: TextStyle(fontSize: 12)),
                            Text("250 SEK", style: TextStyle(fontSize: 12)),
                            Text("350 SEK", style: TextStyle(fontSize: 12)),
                            Text("400 SEK", style: TextStyle(fontSize: 12)),
                          ],
                        ),
                        const SizedBox(height: 14),
                      ],
                    )),

                // ---- Description ----
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Description",
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(14),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: TextField(
                    controller: controller.descController,
                    minLines: 4,
                    maxLines: 8,
                    decoration: const InputDecoration(
                      hintText: "Add Description",
                      border: InputBorder.none,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(height: 22),

                // ---- Upload Photos ----
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "Upload Photos",
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() => GestureDetector(
                      onTap: controller.pickImages,
                      child: Container(
                        width: double.infinity,
                        height: 100,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: Colors.grey.shade300, width: 1.1),
                          borderRadius: BorderRadius.circular(14),
                          color: Colors.grey[100],
                        ),
                        child: controller.images.isEmpty
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: const [
                                  Icon(Icons.upload,
                                      color: Color(0xFF5B20C7), size: 30),
                                  SizedBox(height: 6),
                                  Text("Upload Photo",
                                      style: TextStyle(color: Colors.grey)),
                                ],
                              )
                            : ListView(
                                scrollDirection: Axis.horizontal,
                                children: controller.images
                                    .map((img) => Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            child: Image.file(
                                              File(img.path),
                                              height: 80,
                                              width: 80,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ))
                                    .toList(),
                              ),
                      ),
                    )),
                const SizedBox(height: 38),

                // ---- Done Button ----
                SizedBox(
                  width: double.infinity,
                  height: 47,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorMain,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    onPressed: () {
                      order.description = controller.descController.text;
                      order.images =
                          controller.images.map((img) => img.path).toList();
                      order.minPrice = controller.priceRange.value.start;
                      order.maxPrice = controller.priceRange.value.end;

                      print(order.toJson()); // للتأكد فقط
                      Get.toNamed(Pages.order_summary.value, arguments: order);
                    },
                    child: const Text(
                      "Done",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.5,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
