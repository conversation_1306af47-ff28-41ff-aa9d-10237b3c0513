import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/features/home/<USER>/orders_model.dart';

import '../../core/services/rest_api/rest_api.dart';
import '../../core/services/state_management/obs.dart';
import 'package:flutter/material.dart';

import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/config/app_builder.dart';

class StoryModel {
  final String title;
  final String imageUrl;
  final String description;
  final String logoUrl;

  StoryModel(
      {required this.title,
      required this.imageUrl,
      required this.description,
      this.logoUrl = ""});
}

class HomePageController extends GetxController {
  final RxList<StoryModel> stories = <StoryModel>[].obs;
  // ObsList<OrderModel> orders = ObsList([]);
  late PaginationController pagerController;

  Future<ResponseModel> fetchOrdersPro(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.all_orders_list,
        params: {
          "page": page,
        },
        cancelToken: cancel,
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
  // Future<void> fetchOrdersPro() async {
  //   ResponseModel response = await APIService.instance.request(
  //     Request(
  //       endPoint: EndPoints.all_orders_list, // غيّرها لمسار الـ API الصحيح عندك
  //       method: RequestMethod.Get,
  //       fromJson: OrderModel.fromJson,
  //       //  (json) =>
  //       // List<OrderModel>.from(json.map((e) => OrderModel.fromJson(e))),
  //       copyHeader: {
  //         'Accept': 'application/json',
  //         'Content-Type': 'application/json'
  //       },
  //     ),
  //   );

  //   if (response.success) {
  //     orders.value = response.data;
  //   } else {
  //     orders.error = response.message;
  //   }
  // }

  @override
  void onInit() {
    // fetchOrdersPro();
    super.onInit();
    loadStories();
  }

  void loadStories() {
    // In a real app, you'd fetch this from an API or local database

    stories.value = [
      StoryModel(
          title: "Purple Story",
          imageUrl:
              "https://images.unsplash.com/photo-1516637297111-d691255d2796?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          description: "...",
          logoUrl:
              "https://assets.stickpng.com/images/58429963a6515b1e0ad75ade.png"),
      StoryModel(
        title: "Laptop Story",
        imageUrl:
            "https://images.unsplash.com/photo-1626084071453-6eac381cde2a?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        description: "...",
      ),
      StoryModel(
        title: "Insurance Story",
        imageUrl:
            "https://images.unsplash.com/photo-1568992688000-019b681082c6?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        description: "...",
      ),
    ];
  }
}
