name: renvo_app
# description: "A new Flutter project."
# publish_to: 'none'
# version: 0.1.0

# environment:
#   sdk: ^3.6.2

# dependencies:
#   flutter:
#     sdk: flutter

# dev_dependencies:
#   flutter_test:
#     sdk: flutter
#   flutter_lints: ^5.0.0

# flutter:
#   uses-material-design: true


# name: shop_app
# description: "A new Flutter project."
# # The following line prevents the package from being accidentally published to
# # pub.dev using `flutter pub publish`. This is preferred for private packages.
# publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# # The following defines the version and build number for your application.
# # A version number is three numbers separated by dots, like 1.2.43
# # followed by an optional build number separated by a +.
# # Both the version and the builder number may be overridden in flutter
# # build by specifying --build-name and --build-number, respectively.
# # In Android, build-name is used as versionName while build-number used as versionCode.
# # Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# # In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# # Read more about iOS versioning at
# # https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# # In Windows, build-name is used as the major, minor, and patch parts
# # of the product and file versions while build-number is used as the build suffix.
# version: 1.0.0+1

# environment:
#   sdk: ^3.6.0

# # Dependencies specify other packages that your package needs in order to work.
# # To automatically upgrade your package dependencies to the latest versions
# # consider running `flutter pub upgrade --major-versions`. Alternatively,
# # dependencies can be manually updated by changing the version numbers below to
# # the latest version available on pub.dev. To see which dependencies have newer
# # versions available, run `flutter pub outdated`.
# dependencies:
#   flutter:
#     sdk: flutter
#   flutter_localizations:
#     sdk: flutter
#   cupertino_icons: ^1.0.8
#   easy_localization: ^3.0.7
#   get: ^4.6.6
#   flutter_svg:
#   cached_network_image: ^3.4.1
#   shimmer: ^3.0.0
#   dio:
#   carousel_slider: ^5.0.0
#   image_picker: ^1.1.2

# dev_dependencies:
#   flutter_test:
#     sdk: flutter

#   # The "flutter_lints" package below contains a set of recommended lints to
#   # encourage good coding practices. The lint set provided by the package is
#   # activated in the `analysis_options.yaml` file located at the root of your
#   # package. See that file for information about deactivating specific lint
#   # rules and activating additional ones.
#   flutter_lints: ^5.0.0

# # For information on the generic Dart part of this file, see the
# # following page: https://dart.dev/tools/pub/pubspec
# flutter_gen:
#   integrations:
#     flutter_svg: true
# # The following section is specific to Flutter packages.
# flutter:

#   # The following line ensures that the Material Icons font is
#   # included with your application, so that you can use the icons in
#   # the material Icons class.
#   uses-material-design: true

#   # To add assets to your application, add an assets section, like this:
#   assets:
#     - assets/translations/
#     - assets/icons/

#   # An image asset can refer to one or more resolution-specific "variants", see
#   # https://flutter.dev/to/resolution-aware-images

#   # For details regarding adding assets from package dependencies, see
#   # https://flutter.dev/to/asset-from-package

#   # To add custom fonts to your application, add a fonts section here,
#   # in this "flutter" section. Each entry in this list should have a
#   # "family" key with the font family name, and a "fonts" key with a
#   # list giving the asset and other descriptors for the font. For
#   # example:
#   # fonts:
#   #   - family: Schyler
#   #     fonts:
#   #       - asset: fonts/Schyler-Regular.ttf
#   #       - asset: fonts/Schyler-Italic.ttf
#   #         style: italic
#   #   - family: Trajan Pro
#   #     fonts:
#   #       - asset: fonts/TrajanPro.ttf
#   #       - asset: fonts/TrajanPro_Bold.ttf
#   #         weight: 700
#   #
#   # For details regarding fonts from package dependencies,
#   # see https://flutter.dev/to/font-from-package

description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.5 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # flutter_localizations:
    # sdk: flutter
  cupertino_icons: ^1.0.8
  # easy_localization: 
  get: 
  flutter_svg:
  cached_network_image: 
  shimmer: 
  dio:
  carousel_slider:
  image_picker: ^1.1.2
  get_storage:
  pinput: ^5.0.1
  range_slider_flutter: ^0.0.2
  flutter_rating_bar: ^4.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_gen:
  integrations:
    flutter_svg: true
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/translations/
    - assets/icons/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
