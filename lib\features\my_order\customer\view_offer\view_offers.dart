// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:renvo_app/core/constants/controllers_tags.dart';
// import 'package:renvo_app/core/services/pagination/options/list_view.dart';
// import 'package:renvo_app/core/style/repo.dart';
// import 'package:renvo_app/features/my_order/controller.dart';
// import 'package:renvo_app/features/my_order/models/view_offer.dart';

// class ViewOffersPage extends StatelessWidget {
//   const ViewOffersPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final args = Get.arguments;
//     final controller = Get.find<MyOrderPageController>();

//     return Scaffold(
//       appBar: AppBar(),
//       body: ListViewPagination<VOfferModel>.separated(
//         tag: ControllersTags.view_offer_pager,
//         fetchApi: controller.viewOffer(args),
//         fromJson: VOfferModel.fromJson,
//         itemBuilder: (context, index, offer) {
//           return Padding(
//             padding: EdgeInsets.only(bottom: 13),
//             child: _OrderCard(offer: offer),
//           );
//         },
//         separatorBuilder: (context, index) => SizedBox(height: 16),
//       ),
//     );
//   }
// }

// class _OrderCard extends StatelessWidget {
//   final VOfferModel offer;
//   const _OrderCard({required this.offer});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(19),
//         border: Border.all(color: Colors.grey.shade100),
//         boxShadow: [
//           BoxShadow(
//             color: Color(0xFF5B20C7).withValues(alpha: 0.09),
//             blurRadius: 12,
//             offset: Offset(0, 4),
//           )
//         ],
//       ),
//       child: Row(
//         children: [
//           // شريط جانبي بنفسجي
//           Container(
//             width: 6,
//             height: 120,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(19),
//                 bottomLeft: Radius.circular(19),
//               ),
//               gradient: LinearGradient(
//                 colors: [
//                   StyleRepo.deepBlueFegma,
//                   StyleRepo.deebBlue2,
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//           ),
//           Expanded(
//             child: Padding(
//               padding: const EdgeInsets.fromLTRB(16, 14, 14, 14),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Order ID
//                   Text(
//                     "Offer #${offer.id}",
//                     style: TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 15.7,
//                       color: Color(0xFF323232),
//                     ),
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                   // SizedBox(height: 8),
//                   // Text(
//                   //   "Pro_id #${offer.}",
//                   //   style: TextStyle(
//                   //     fontWeight: FontWeight.bold,
//                   //     fontSize: 15.7,
//                   //     color: Color(0xFF323232),
//                   //   ),
//                   //   overflow: TextOverflow.ellipsis,
//                   // ),
//                   SizedBox(height: 8),
//                   // الوصف
//                   Row(
//                     children: [
//                       Text(
//                         offer.providerName,
//                         style: TextStyle(
//                           color: Color(0xFF9093A3),
//                           fontSize: 13.5,
//                         ),
//                       ),
//                       Icon(Icons.description, color: Colors.grey, size: 17),
//                       SizedBox(width: 4),
//                       Expanded(
//                         child: Text(
//                           offer.description,
//                           style: TextStyle(
//                             color: Color(0xFF9093A3),
//                             fontSize: 13.5,
//                           ),
//                           maxLines: 2,
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: 8),
//                   // التاريخ
//                   Row(
//                     children: [
//                       Icon(Icons.calendar_today, color: Colors.grey, size: 16),
//                       SizedBox(width: 4),
//                       Text(
//                         offer.price,
//                         style: TextStyle(
//                           fontSize: 13.3,
//                           color: Colors.grey[800],
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: 8),
//                   // الحالة والوقت
//                   Row(
//                     children: [
//                       Icon(Icons.info_outline, color: Colors.grey, size: 17),

//                       SizedBox(width: 4),
//                       ElevatedButton(style: ButtonStyle(backgroundColor: ),
//                         onPressed: (){}, child: Text("data")),
//                       Spacer(),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
