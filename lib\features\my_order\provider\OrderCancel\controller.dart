import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderCancelProController extends GetxController {
  // var orders = <MyOrderModel>[].obs;

  // @override
  // void onInit() {
  //   super.onInit();
  //   fetchPendingOrders();
  // }

  // void fetchPendingOrders() async {
  //   await Future.delayed(Duration(seconds: 1)); // simulation
  //   orders.value = List.generate(
  //     5,
  //     (i) => MyOrderModel(
  //         id: i, title: "Pending Order ${i + 1}", status: OrderStatus.pending),
  //   );
  // }

  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.provider_orders,
        params: {"page": page, "status": "cancelled"},
        cancelToken: cancel,
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
