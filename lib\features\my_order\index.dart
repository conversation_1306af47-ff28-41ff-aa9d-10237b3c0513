import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/customer/OrderCancel/index.dart';
import 'package:renvo_app/features/my_order/customer/OrderComplete/index.dart';
import 'package:renvo_app/features/my_order/customer/OrderPending/index.dart';
import 'package:renvo_app/features/my_order/customer/OrderUnderway/index.dart';
import 'package:renvo_app/features/my_order/provider/OrderCancel/index.dart';
import 'package:renvo_app/features/my_order/provider/OrderComplete/index.dart';
import 'package:renvo_app/features/my_order/provider/OrderPending/index.dart';
import 'package:renvo_app/features/my_order/provider/OrderUnderway/index.dart';
import 'package:renvo_app/features/my_order/widgets/order_card_pending_customer.dart';

// class MyOrderPage extends StatelessWidget {
//   MyOrderPage({super.key});
//   final MyOrderPageController controller = Get.put(MyOrderPageController());

//   Widget build(BuildContext context) {
//     final controller = Get.put(MyOrderPageController());

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text("My Order"),
//         bottom: TabBar(
//           controller: controller.tabController,
//           tabs: const [
//             Tab(text: "waiting"),
//             Tab(text: "processing"),
//             Tab(text: "completed"),
//             Tab(text: "cancelled"),
//           ],
//         ),
//       ),
//       body: TabBarView(
//         controller: controller.tabController,
//         children: const [
//           OrderrCard(),
//           OrderrCard(),
//           OrderrCard(),
//           OrderrCard()
//         ],
//       ),
//     );
//   }
// }

// class OrderCard extends StatelessWidget {
//   final String id;
//   final String dateTime;
//   final String serviceCategory;
//   final String serviceSubcategory;
//   final String description;
//   final String location;
//   final bool showRating; // true إذا كنا في completed
//   final double rating; // التقييم بين 0 و 5

//   const OrderCard({
//     super.key,
//     required this.id,
//     required this.dateTime,
//     required this.serviceCategory,
//     required this.serviceSubcategory,
//     required this.description,
//     required this.location,
//     this.showRating = false,
//     this.rating = 0.0,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       padding: const EdgeInsets.all(12),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black12,
//             blurRadius: 4,
//             offset: Offset(0, 2),
//           )
//         ],
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // الجزء العلوي
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 "ID  $id",
//                 style:
//                     TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
//               ),
//               Text(
//                 dateTime,
//                 style: TextStyle(color: Colors.grey),
//               ),
//             ],
//           ),
//           const SizedBox(height: 12),
//           // عنوان الخدمة
//           Row(
//             children: [
//               CircleAvatar(
//                 radius: 18,
//                 backgroundColor: Colors.deepPurple,
//                 child: Icon(Icons.store, color: Colors.white),
//               ),
//               const SizedBox(width: 12),
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     serviceCategory,
//                     style: const TextStyle(
//                         fontWeight: FontWeight.bold, fontSize: 16),
//                   ),
//                   Text(
//                     serviceSubcategory,
//                     style: TextStyle(color: Colors.grey.shade600),
//                   )
//                 ],
//               )
//             ],
//           ),
//           const SizedBox(height: 12),
//           Divider(),
//           const SizedBox(height: 4),
//           // الوصف
//           Text(
//             description,
//             style: TextStyle(fontSize: 14, color: Colors.black),
//           ),
//           const SizedBox(height: 8),
//           Divider(),
//           const SizedBox(height: 8),
//           // الموقع
//           Row(
//             children: [
//               Icon(Icons.location_on_outlined, color: Colors.grey, size: 18),
//               const SizedBox(width: 6),
//               Expanded(
//                 child: Text(
//                   location,
//                   style: TextStyle(
//                       fontWeight: FontWeight.w500, color: Colors.grey.shade700),
//                 ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 16),
//           // إما Rating أو View
//           Align(
//             alignment: Alignment.centerLeft,
//             child: showRating
//                 ? Row(
//                     children: List.generate(5, (index) {
//                       return Icon(
//                         index < rating
//                             ? Icons.star
//                             : Icons.star_border_outlined,
//                         size: 20,
//                         color: Colors.amber,
//                       );
//                     }),
//                   )
//                 : TextButton(
//                     onPressed: () {},
//                     child: Text(
//                       "View",
//                       style: TextStyle(color: Colors.blue),
//                     ),
//                   ),
//           ),
//         ],
//       ),
//     );
//   }
// }

class MyOrderPage extends StatelessWidget {
  MyOrderPage({super.key});
  final MyOrderPageController controller = Get.put(MyOrderPageController());
  final appBuilder = Get.find<AppBuilder>();
  final List<Widget> orderCustomerPages = const [
    OrderPendingPage(),

    OrderProcessingPage(),
    OrderCompletePage(),
    OrderCancelPage()
    // SizedBox(),
    // SizedBox(),

    // OrderCompletedPage(),
    // OrderCancelledPage(),
  ];
  final List<Widget> orderProviderPages = const [
    OrderPendingProPage(),
    OrderProcessingProPage(),
    OrderCompleteProPage(),
    // SizedBox(),
    // SizedBox(),
    OrderCancelProPage()
    // OrderPendingPage(),
    // OrderProcessingPage(),
    // OrderCompletedPage(),
    // OrderCancelledPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: const Text("My Orders"),
        bottom: TabBar(
          // dividerColor: StyleRepo.deepBlueFegma,
          indicatorColor: StyleRepo.deepBlueFegma,
          labelColor: StyleRepo.deepBlueFegma,
          controller: controller.tabController,
          tabs: const [
            Tab(text: "Waiting"),
            Tab(text: "Processing"),
            Tab(text: "Completed"),
            Tab(text: "Cancelled"),
          ],
        ),
      ),
      body: Builder(builder: (context) {
        return Obx(() {
          // الشرط هنا حسب وضع التطبيق الحالي
          if (appBuilder.isProviderMode.value) {
            return TabBarView(
              controller: controller.tabController,
              children: orderProviderPages,
            );
          } else {
            return TabBarView(
              controller: controller.tabController,
              children: orderCustomerPages,
            );
          }
        });
      }),
      // TabBarView(
      //   controller: controller.tabController,
      //   children: orderPages,
      // ),
    );
  }
}
