import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/features/add_offer/add_offer2/controller.dart';

class ReviewOfferPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddOfferReviewController());
    // final args = Get.arguments;
    // final List<XFile> images = (args['images'] as RxList<XFile>).toList();

    return Scaffold(
      appBar: AppBar(
        title: Text("Review Offer"),
        backgroundColor: Color(0xFF5B20C7),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: ListView(
          children: [
            ListTile(
              title:
                  Text('Price', style: TextStyle(fontWeight: FontWeight.bold)),
              subtitle: Text('${controller.price}'),
            ),
            ListTile(
              title: Text('Duration',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              subtitle: Text('${controller.duration} ${controller.unit}'),
            ),
            ListTile(
              title: Text('Description',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              subtitle: Text('${controller.description}'),
            ),
            SizedBox(height: 14),
            Text('Images:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 6),
            // هيي لاااااااااااااااااااااااااااااااااااااااازم ارجعلاااااا
            // images.isNotEmpty
            //     ? SizedBox(
            //         height: 110,
            //         child: ListView.builder(
            //           scrollDirection: Axis.horizontal,
            //           itemCount: images.length,
            //           itemBuilder: (context, i) {
            //             return Padding(
            //               padding: const EdgeInsets.all(6.0),
            //               child: ClipRRect(
            //                 borderRadius: BorderRadius.circular(8),
            //                 child: Image.file(
            //                   File(images[i].path),
            //                   height: 90,
            //                   width: 90,
            //                   fit: BoxFit.cover,
            //                 ),
            //               ),
            //             );
            //           },
            //         ),
            //       )
            //     : Text('No images selected.'),
            SizedBox(height: 28),
            ElevatedButton(
              onPressed: () {
                controller.sendOfferToApi();
                // هنا تضع منطق إرسال البيانات للباك
                // print(args) أو API POST
              },
              child: Text("Send Offer"),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF5B20C7),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
            )
          ],
        ),
      ),
    );
  }
}
