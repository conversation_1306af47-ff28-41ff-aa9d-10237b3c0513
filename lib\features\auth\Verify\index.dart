import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/features/auth/Verify/controller.dart';

import 'package:pinput/pinput.dart';
import 'package:renvo_app/gen/assets.gen.dart';

class VerifyPage extends StatelessWidget {
  const VerifyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyController());
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(0, 51, 153, 1),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Stack(
        // alignment: Alignment.bottomCenter,
        children: [
          Container(
            width: MediaQuery.sizeOf(context).width,
            height: MediaQuery.sizeOf(context).height * 0.25,
            color: const Color.fromRGBO(0, 51, 153, 1),
            child: Align(
              alignment: Alignment.center,
              child: AppImage(
                path: Assets.icons.logoWhite.path,
                type: ImageType.Asset,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20), color: Colors.white),
              height: MediaQuery.sizeOf(context).height * 0.80,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 30),
                    child: Text(
                      "Verify Phone Number ",
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 26,
                      ),
                    ),
                  ),
                  Text("Enter the Code Send To *** *** **45"),
                  Padding(
                    padding: EdgeInsets.only(top: 30, right: 30, left: 80),
                    child: Pinput(
                      length: 4,
                      controller: controller.verifyController,
                      onCompleted: controller.onOtpCompleted,
                      defaultPinTheme: PinTheme(
                        width: 50,
                        height: 56,
                        textStyle: const TextStyle(fontSize: 20),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey),
                        ),
                      ),
                    ),
                  ),
                  // Resend Code Button
                  Obx(() => TextButton(
                        onPressed: controller.isResendEnabled.value
                            ? controller.resendCode
                            : null,
                        child: controller.isResendEnabled.value
                            ? const Text("Resend Code")
                            : Text(
                                "Resend in ${controller.secondsRemaining.value}s",
                                style: const TextStyle(color: Colors.grey),
                              ),
                      )),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          // const SizedBox(height: 32),
          Positioned(
            bottom: 100,
            left: 150,
            child: ElevatedButton(
              onPressed: controller.submitOtp,
              style: ElevatedButton.styleFrom(
                backgroundColor: StyleRepo.deepBlueFegma,
                padding:
                    const EdgeInsets.symmetric(vertical: 14, horizontal: 32),
              ),
              child: const Text("Confirm"),
            ),
          ),
        ],
      ),
    );
  }
}
