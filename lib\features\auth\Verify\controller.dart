import 'dart:async';

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:renvo_app/core/routes/routes.dart';

class VerifyController extends GetxController {
  final verifyController = TextEditingController();

  var isResendEnabled = false.obs;
  var secondsRemaining = 60.obs;
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    startResendTimer();
  }

  void startResendTimer() {
    isResendEnabled.value = false;
    secondsRemaining.value = 60;

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        isResendEnabled.value = true;
        _timer?.cancel();
      }
    });
  }

  void resendCode() {
    print("Code resent!");
    startResendTimer();
  }

  void onOtpCompleted(String code) {
    print("OTP Completed: $code");
    // هنا يمكن استدعاء API أو التنقل للصفحة التالية
  }

  void submitOtp() {
    final code = verifyController.text;
    if (code.length == 4) {
      print("Submitting: $code");
      Get.toNamed(Pages.home.value);
      // منطق التحقق من الكود هنا
    } else {
      Get.snackbar("Error", "Please enter a 4-digit code",
          backgroundColor: Colors.redAccent, colorText: Colors.white);
    }
  }

  @override
  void onClose() {
    verifyController.dispose();
    super.onClose();
  }
}
