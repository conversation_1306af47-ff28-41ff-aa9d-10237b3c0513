import 'dart:async';

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:renvo_app/core/routes/routes.dart';

class VerifyController extends GetxController {
  final verifyController = TextEditingController();

  var isResendEnabled = false.obs;
  var secondsRemaining = 60.obs;
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    startResendTimer();
  }

  void startResendTimer() {
    isResendEnabled.value = false;
    secondsRemaining.value = 60;

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        isResendEnabled.value = true;
        _timer?.cancel();
      }
    });
  }

  void resendCode() {
    print("Code resent!");
    startResendTimer();
  }

  void onOtpCompleted(String code) {
    print("OTP Completed: $code");
    // هنا يمكن استدعاء API أو التنقل للصفحة التالية
  }

  void submitOtp() {
    final code = verifyController.text;
    //   if (code.length == 4) {
    //     print("Submitting: $code");
    //     Get.toNamed(Pages.home.value);
    //     // منطق التحقق من الكود هنا
    //   } else {
    //     Get.snackbar("Error", "Please enter a 4-digit code",
    //         backgroundColor: Colors.redAccent, colorText: Colors.white);
    //   }
  }

  Future<void> _makeVerificationAPICall(String code) async {
    // if (cleanPhone == null || dialCode == null) {
    // _showError('Phone number data missing. Please try again.');
    // Get.offAllNamed(Pages.login.value);
    // return;
    // }

    //   Map<String, dynamic> jsonData = {
    //     'phone': cleanPhone!,
    //     'dial_country_code': dialCode!,
    //     'otp': code,
    //     'device_token': 'FLUTTER_APP',
    //   };

    //   ResponseModel response = await APIService.instance.request(
    //     Request(
    //       endPoint: EndPoints.verifyOtp,
    //       method: RequestMethod.Post,
    //       body: jsonData,
    //       copyHeader: {
    //         'Accept': 'application/json',
    //         'Content-Type': 'application/json'
    //       },
    //     ),
    //   );

    //   print(' Verification response: ${response.data}');
    //   if (response.success && response.data != null) {
    //     _handleVerificationSuccess(response);
    //   } else {
    //     _handleVerificationError(response);
    //   }
    // }

    @override
    void onClose() {
      verifyController.dispose();
      super.onClose();
    }
  }
}
