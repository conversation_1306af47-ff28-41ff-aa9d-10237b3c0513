import 'package:flutter/material.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/gen/assets.gen.dart';
// import 'package:/gen/assets.gen.dart';

class AppDialogs {
  /// Dialog تأكيد الحذف
  static void showDeleteConfirmation({
    required BuildContext context,
    required VoidCallback onDelete,
    String title = "Delete Confirmation",
    String message =
        "Are you sure you want to delete this suggestion? This action cannot be undone.",
  }) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة سلة الحذف
              Assets.images.deletePng
                  .image(width: 90, height: 90, fit: BoxFit.contain),
              SizedBox(height: 18),
              Text(
                title,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
              SizedBox(height: 12),
              Text(
                message,
                style: TextStyle(
                    fontSize: 16,
                    color: const Color.fromARGB(255, 106, 104, 104)),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 28),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text("Cancel", style: TextStyle(color: Colors.grey)),
                  ),
                  SizedBox(width: 12),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(); // أغلق الـ Dialog
                      onDelete(); // نفذ الحذف
                    },
                    child:
                        Text("Delete", style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  static void showCustomMessage({
    required BuildContext context,
    required String title,
    required String message,
    required bool isSuccess,
    VoidCallback? onConfirm,
  }) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة في الأعلى
              CircleAvatar(
                radius: 34,
                backgroundColor:
                    isSuccess ? Colors.green.shade100 : Colors.red.shade100,
                child: Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  size: 44,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
              ),
              SizedBox(height: 18),
              Text(
                title,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
              SizedBox(height: 12),
              Text(
                message,
                style: TextStyle(fontSize: 16, color: Colors.grey[800]),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 28),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text("إغلاق", style: TextStyle(color: Colors.grey)),
                  ),
                  if (onConfirm != null) ...[
                    SizedBox(width: 12),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isSuccess ? Colors.green : Colors.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                        onConfirm();
                      },
                      child: Text("تأكيد"),
                    ),
                  ]
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
