class OrderDataModel {
  int? categoryId;
  int? subCategoryId;
  dynamic serviceType; // 0: asap, 1: specific date
  DateTime? selectedDate;
  String? selectedTime; // 24 ساعة: "14:00" مثلاً
  String? description;
  List<String>? images; // روابط الصور أو المسارات المحلية
  double? minPrice;
  double? maxPrice;
  OrderDataModel({
    this.categoryId,
    this.subCategoryId,
    this.serviceType,
    this.selectedDate,
    this.selectedTime,
    this.description,
    this.images,
    this.minPrice,
    this.maxPrice,
  });

  // التحويل إلى Json (مثالي للإرسال للباك اند)
  Map<String, dynamic> toJson() {
    return {
      'categoryId': categoryId,
      'subCategoryId': subCategoryId,
      'serviceType': serviceType,
      'selectedDate': selectedDate?.toIso8601String(),
      'selectedTime': selectedTime,
      'description': description,
      'images': images,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
    };
  }

  // تحويل من Json (اختياري لو تحتاج)
  factory OrderDataModel.fromJson(Map<String, dynamic> json) {
    return OrderDataModel(
      categoryId: json['categoryId'],
      subCategoryId: json['subCategoryId'],
      serviceType: json['serviceType'],
      minPrice: json['minPrice'],
      maxPrice: json['maxPrice'],
      selectedDate: json['selectedDate'] != null
          ? DateTime.parse(json['selectedDate'])
          : null,
      selectedTime: json['selectedTime'],
      description: json['description'],
      images: json['images'] != null ? List<String>.from(json['images']) : [],
    );
  }
}
