import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/style.dart';

import 'core/services/rest_api/rest_api.dart';

// Future<void> main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   await EasyLocalization.ensureInitialized();
//   runApp(
//     EasyLocalization(
//       supportedLocales: AppLocalization.values.map((e) => e.locale).toList(),
//       path: "assets/translations",
//       // assetLoader: const CodegenLoader(),
//       fallbackLocale: AppLocalization.en.locale,
//       child: const MyApp(),
//     ),
//   );
// }

// class MyApp extends StatelessWidget {
//   const MyApp({super.key});

//   // This widget is the root of your application.
//   @override
//   Widget build(BuildContext context) {
//     Get.put(APIService());
//     return GetMaterialApp(
//       title: 'Flutter Demo',
//       theme: AppStyle.theme,
//       localizationsDelegates: context.localizationDelegates,
//       locale: context.locale,
//       supportedLocales: context.supportedLocales,
//       //
//       initialRoute: '/',
//       unknownRoute: AppRouting.unknownRoute,
//       getPages: AppRouting.routes,
//     );
//   }
// }
// import 'dart:convert';
// import 'dart:typed_data';

// import 'package:flutter/material.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:excel/excel.dart';
// import 'package:get/get.dart';
// import 'package:invisio_app/analyze/index.dart';

// void main() {
//   runApp(MyApp());
// }

// class MyApp extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       title: 'Excel & CSV Uploader',
//       debugShowCheckedModeBanner: false,
//       home: AnalyzePage(), // ← هنا تستدعي الواجهة
//     );
//   }
// }

main() {
  runApp(
    const MyApp(),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    // Get.put(APIService());
    Get.put(AppBuilder());
    // final SplashScreenController controller = Get.put(SplashScreenController());

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Flutter Demo',
      theme: AppStyle.theme,

      //
      initialRoute: '/',
      unknownRoute: AppRouting.unknownRoute,
      getPages: AppRouting.routes,
    );
  }
}
