class MainProduct {
  late int id;
  late String name;
  late String description;
  late int totalPrice;
  late String image;
  int? priceBeforeDiscount;

  MainProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.totalPrice,
    required this.image,
    this.priceBeforeDiscount,
  });

  MainProduct.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    description = json["description"];
    totalPrice = json["total_price"];
    image = json["image"] ?? "";
    priceBeforeDiscount = json["price_before_discount"];
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "total_price": totalPrice,
        "image": image,
        "price_before_discount": priceBeforeDiscount,
      };
}
