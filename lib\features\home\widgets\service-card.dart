import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/add/controller.dart';
import 'package:renvo_app/gen/assets.gen.dart';
import 'package:shimmer/shimmer.dart';

class buildServiceGrid extends StatelessWidget {
  const buildServiceGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildServiceGrid();
  }
}

Widget _buildServiceGrid() {
  final controller = Get.put(AddPageController());
  return ObsListBuilder(
      obs: controller.categories,
      builder: (context, categories) {
        return GridView.builder(
          physics: const ClampingScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.1,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return _buildServiceCard(
                category.title, category.providerCount.toString());
          },
        );
      });
}

Widget _buildServiceCard(
  String title,
  String subtitle,
) {
  final services = [
    {
      'icon': Assets.images.homeService.image(),
    },
    {
      'icon': Assets.images.homeService.image(),
    },
  ];

  // final service = services[index];

  // var service = services[0];
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    period: const Duration(seconds: 2),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(17),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25.8, sigmaY: 25.8),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                // StyleRepo.lightGrey.withValues(alpha: 0.10),
                // StyleRepo.lightGrey.withValues(alpha: 0.30),
                Colors.white.withOpacity(0.05), // خفيف جدًا
                Colors.white.withOpacity(0.15),
              ],
            ),
            borderRadius: BorderRadius.circular(17),
            boxShadow: [
              BoxShadow(
                color: const Color.fromARGB(255, 129, 126, 126)
                    .withValues(alpha: 0.08),
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                services[0]['icon'] as Widget,
                SizedBox(height: 6),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                // SizedBox(height: 10),
                // Align(
                //   alignment: Alignment.center,
                //   child: Text(
                //     "proCunt ${subtitle}",
                //     style: TextStyle(
                //       color: Colors.white.withValues(alpha: 0.8),
                //       fontSize: 17,
                //       fontWeight: FontWeight.w600,
                //       // height: 1.2,
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
