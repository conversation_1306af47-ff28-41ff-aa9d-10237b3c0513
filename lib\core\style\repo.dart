import 'dart:ui';

import 'package:flutter/material.dart';

class StyleRepo {
  static const green = Color(0xFF53B175);

  static const white = Color(0xFFFCFCFC);
  static const lightGrey = Color(0xFFB1B1B1);
  static const darkGrey = Color(0xFF7C7C7C);
  static const black = Color(0xFF030303);

  static const red = Colors.red;
  static const yellow = Colors.amber;

  static const sandyBrown = Color(0xFFF8A44C);
  static const deepBlue = Color(0xFF2738B4);
  static const deepBlueFegma = Color(0xFF003399);

  static const deeppurple = Color.fromARGB(255, 39, 0, 188);
  static const deebBlue2 = Color(0xff0048D9);
  static const lightBlue1 = Color(0xFF859FBE);
  static const lightBlue2 = Color.fromARGB(255, 63, 89, 126);
  static const blue = Color(0xFF204E78);
  static const gg = Color(0xFFF3F3F3);
  // F3F3F3
}
