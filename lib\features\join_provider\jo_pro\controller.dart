import 'package:flutter/material.dart';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/join_provider/model/all_categories.dart';

class ProviderController1 extends GetxController {
  ObsList<ProviderCategory> categories = ObsList([]);
  // الحقول
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final descriptionController = TextEditingController();
  late final int id;
  @override
  onInit() {
    id = Get.arguments;
    super.onInit();
  }

  // قيم للجندر
  var selectedGender = "Male".obs;

  // وقت العمل
  var workFrom = "".obs;
  var workTo = "".obs;

  // Dio instance

  // دالة ارسال البيانات للـ API
  Future<void> registerProvider() async {
    // final data = {
    //   "name": nameController.text,
    //   "email": emailController.text,
    //   "phone": phoneController.text,
    //   "gender": selectedGender.value,
    //   "work_from": workFrom.value,
    //   "work_to": workTo.value,
    //   "description": descriptionController.text,
    // };
    FormData formData = FormData();

    formData.fields
          ..add(MapEntry('name', nameController.text ?? ''))
          ..add(MapEntry('email', emailController.text ?? ''))
          ..add(MapEntry('phone', phoneController.text ?? ''))
          // ..add(MapEntry('gender', selectedGender.value ?? ''))
          ..add(MapEntry('start_at', workFrom.value ?? ''))
          ..add(MapEntry('end_at', workTo.value))
          ..add(MapEntry('description', descriptionController.text))
          ..add(MapEntry('country_id', "1"))
          ..add(MapEntry('city_id', "1"))
          ..add(MapEntry('street_name', "test"))
          ..add(MapEntry('street_number', "12345"))
          ..add(MapEntry('post_code', "455"))
          ..add(MapEntry('longitude', "455"))
          ..add(MapEntry('latitude', "455"))
          ..add(MapEntry('prv_category_ids[0]', "${id}"))
          ..add(MapEntry('dial_country_code', "9667"))

        // ..add(MapEntry('prv_category_id', order.subCategoryId?.toString() ?? ''))
        // ..add(MapEntry('main_category_id', order.categoryId?.toString() ?? ''))

        ;
    final response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.join_as_provider,
          method: RequestMethod.Post,
          body: formData,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );

    if (response.success && response.data != null) {
      final rawList = response.data;
      print("sssssssssssssssssssssssssssss${rawList}");
      Get.until(
        (route) => route.settings.name == Pages.home.value,
      );
      // categories.value = rawList.map((e) => AllCategory.fromJson(e)).toList();
    } else {
      print(response.message ?? "Failed to load categories");
    }

    // if (response.statusCode == 200 || response.statusCode == 201) {
    Get.snackbar("Success", "Provider registered successfully");
    // } else {
    Get.snackbar("Error", "Failed to register provider");
  }
  // } catch (e) {

  // }

  @override
  void onClose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    descriptionController.dispose();
    super.onClose();
  }
}
