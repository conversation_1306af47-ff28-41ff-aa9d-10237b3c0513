import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/add/index.dart';
import 'package:renvo_app/features/home/<USER>';
import 'package:renvo_app/features/main/controller.dart';
import 'package:renvo_app/features/my_order/index.dart';
import 'package:renvo_app/features/profile/index.dart';

import 'widgets/nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MainPageController());
    return Scaffold(
      bottomNavigationBar: NavBar(),
      body: Obx(
        () => switch (controller.currentPage.value) {
          0 => HomePage(),
          1 => MyOrderPage(),
          2 => AddPage(),
          4 => ProfilePage(),
          _ => ColoredBox(color: StyleRepo.deepBlueFegma),
        },
      ),
    );
  }
}
