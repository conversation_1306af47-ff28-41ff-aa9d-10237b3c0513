import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/customer/OrderComplete/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderCardCompleteCustomer extends StatelessWidget {
  final MyOrderModel order;
  const OrderCardCompleteCustomer(this.order, {super.key});

  @override
  Widget build(BuildContext context) {
    // final controller = Get.put(SuggestionsPageController());
    // final MyOrderPageController controller = Get.put(MyOrderPageController());
    // final controller = Get.find<MyOrderPageController>();
    final controller = Get.find<OrderCompleteController>();
    final review = controller.reviews[order.id];
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          border: Border.symmetric(
            vertical: BorderSide(
                color: Colors.grey.shade300, width: 5), // top & bottom
            horizontal: BorderSide(
                color: Colors.grey.shade300, width: 6), // left & right
          ),
          // اللون الخارجي (الرمادي)
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.white, // اللون الداخلي للكارد
            borderRadius: BorderRadius.circular(16), // منحني داخلي
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// شريط العنوان: ID + الوقت + 3 نقاط
              Row(
                children: [
                  // شوفي هي كيف

                  Text(
                    // "iddd",
                    "ID  ${order.id}",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                  const Spacer(),
                  Text(
                    "date ",
                    // formatDate(suggestion.timestamp), // فقط اليوم والشهر والسنة
                    style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                  ),

                  // Text(
                  //   "${suggestion.timestamp}",
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: Colors.grey.shade600,
                  //   ),
                  // ),
                  const SizedBox(width: 1),
                  IconButton(
                    onPressed: () {
                      // AppDialogs.showDeleteConfirmation(
                      //   context: context,
                      //   onDelete: () async {
                      //     await controller.deleteSuggestion(
                      //         suggestion.id, context);
                      //   },
                      // );
                    },
                    icon: Icon(Icons.more_horiz),
                    color: Colors.grey,
                    iconSize: 18,
                  ),
                ],
              ),
              const SizedBox(
                  height: 1), // هون بكون المسافة بين العنوان واللي فوقا

              /// فئة الخدمة + الرمز
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      // color: Color.fromARGB(255, 116, 140, 169),
                      color: StyleRepo.deepBlue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.home_work_outlined,
                        color: Colors.white, size: 22),
                  ),
                  const SizedBox(width: 7),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        // "title",
                        "${order.mainCategoryTitle}",
                        // "${order.description.split(' ').take(4).join(' ') + (order.description.split(' ').length > 4 ? '...' : '')}",
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
                      Text(
                        // "user id",
                        "${order.subCategoryTitle}",
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  )
                ],
              ),
              const SizedBox(height: 1),
              Divider(color: Colors.grey.shade300),

              /// الوصف
              Text(
                // "descr",
                "${order.description}",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade900,
                ),
              ),
              const SizedBox(height: 4),
              Divider(color: Colors.grey.shade300),
              // const SizedBox(height: 6),
              // Spacer(),

              /// الموقع
              Row(
                children: [
                  const Icon(Icons.location_on, size: 20, color: Colors.grey),
                  Text("${order.location}"),
                ],
              ),
              const SizedBox(height: 1),
              Divider(),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    "Rating & Review",
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  // Spacer(),
                  SizedBox(
                    width: 3,
                  ),
                  Text(order.proName),
                  SizedBox(
                    height: 5,
                  )
                ],
              ),
              Row(
                children: [
                  // إذا كان هناك تقييم محفوظ، اعرضه
                  if (review != null) ...[
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (review["review"] != null &&
                              review["review"].toString().isNotEmpty)
                            Text(
                              review["review"] ?? "",
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey.shade700,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 8),
                          RatingBarIndicator(
                            rating: (review["rate"] as num?)?.toDouble() ?? 0.0,
                            itemBuilder: (context, _) =>
                                const Icon(Icons.star, color: Colors.amber),
                            itemCount: 5,
                            itemSize: 20,
                          ),
                        ],
                      ),
                    ),
                  ] else
                    // إذا لم يكن هناك تقييم، اعرض زر التقييم
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          _openReviewBottomSheet(order.id, order);
                        },
                        child: Text(
                          "Tap to rating please",
                          style: TextStyle(
                            fontSize: 13.5,
                            color: Colors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              )
            ],
          ),
        ));
  }
}

void _openReviewBottomSheet(int orderId, MyOrderModel order) {
  //  MyOrderModel order;
  final c = Get.find<OrderCompleteController>();
  double rate = 4;
  String ratingText = "Very Good";
  final TextEditingController reviewController = TextEditingController();

  Get.bottomSheet(
    Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // العنوان
          const Text(
            "Rating",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 24),

          // صورة مقدم الخدمة
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  StyleRepo.deepBlueFegma,
                  StyleRepo.deebBlue2,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Icon(
              Icons.person,
              color: Colors.white,
              size: 40,
            ),
          ),
          const SizedBox(height: 16),

          // اسم مقدم الخدمة
          Text(
            order.proName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 20),

          // النجوم
          RatingBar.builder(
            initialRating: 4,
            minRating: 1,
            allowHalfRating: false,
            itemCount: 5,
            itemSize: 40,
            itemPadding: const EdgeInsets.symmetric(horizontal: 4),
            itemBuilder: (context, index) => Icon(
              Icons.star,
              color: index < rate ? Colors.amber : Colors.grey.shade300,
            ),
            onRatingUpdate: (value) {
              rate = value;
              // تحديث النص حسب التقييم
              if (value == 1) {
                ratingText = "Poor";
              } else if (value == 2) {
                ratingText = "Fair";
              } else if (value == 3) {
                ratingText = "Good";
              } else if (value == 4) {
                ratingText = "Very Good";
              } else if (value == 5) {
                ratingText = "Excellent";
              }
            },
          ),
          const SizedBox(height: 12),

          // نص التقييم
          Text(
            ratingText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),

          // نص عدد النجوم
          Text(
            "You Rated Services Provider ${rate.toInt()} Stars",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),

          // حقل التعليق
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: reviewController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: "add your Comment",
                hintStyle: TextStyle(color: Colors.grey),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // زر Done
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () async {
                await c.submitReview(
                  orderId,
                  rate,
                  reviewController.text,
                );
                // تحديث الواجهة بعد التقييم الناجح
                // لا حاجة لعمل شيء إضافي لأن الكنترولر يستخدم .obs
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: StyleRepo.deepBlueFegma,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                elevation: 0,
              ),
              child: const Text(
                "Done",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    ),
    isScrollControlled: true,
  );
}
