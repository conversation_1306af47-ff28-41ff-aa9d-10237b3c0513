import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renva0/core/widgets/modern_toast.dart';

import '../../../core/config/app_builder.dart';
import '../../../core/routes/routes.dart';
import '../../../core/services/rest_api/rest_api.dart';

class VerifyPhoneController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController codeController = TextEditingController();

  String? cleanPhone;
  String? dialCode;
  String? fullPhoneNumber;
  bool fromRegistration = false;

  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  final String? initialPhoneNumber;

  VerifyPhoneController({this.initialPhoneNumber});

  @override
  void onClose() {
    phoneController.dispose();
    codeController.dispose();
    super.onClose();
  }

  String? validateVerificationCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Verification code is required';
    }
    if (value.length != 4) {
      return 'Verification code must be 4 digits';
    }
    if (!RegExp(r'^\d{4}$').hasMatch(value)) {
      return 'Invalid verification code format';
    }
    return null;
  }

  Future<void> verifyPhoneNumber() async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;
    errorMessage.value = '';

    try {
      String enteredCode = codeController.text.trim();
      await _makeVerificationAPICall(enteredCode);
    } catch (e) {
      print(' Verification error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _makeVerificationAPICall(String code) async {
    if (cleanPhone == null || dialCode == null) {
      _showError('Phone number data missing. Please try again.');
      Get.offAllNamed(Pages.login.value);
      return;
    }

    Map<String, dynamic> jsonData = {
      'phone': cleanPhone!,
      'dial_country_code': dialCode!,
      'otp': code,
      'device_token': 'FLUTTER_APP',
    };

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.verifyOtp,
        method: RequestMethod.Post,
        body: jsonData,
        copyHeader: {'Accept': 'application/json', 'Content-Type': 'application/json'},
      ),
    );

    print(' Verification response: ${response.data}');
    if (response.success && response.data != null) {
      _handleVerificationSuccess(response);
    } else {
      _handleVerificationError(response);
    }
  }

  void _handleVerificationSuccess(ResponseModel response) {
    try {
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        // Get important values from API response
        String? phoneVerifiedAt = responseData['phone_verified_at'];
        int isCompleted = responseData['is_completed'] ?? 0;

        final appBuilder = Get.find<AppBuilder>();
        appBuilder.updateFromAPIResponse(responseData);

        if (phoneVerifiedAt != null && isCompleted == 1) {
          // User is fully verified and profile complete
          _showSuccess('Phone verified successfully! Welcome!');
          Future.delayed(Duration(milliseconds: 500), () {
            Get.offAllNamed(Pages.home.value);
          });
        } else if (phoneVerifiedAt != null && isCompleted != 1) {
          // User verified but needs to complete profile
          _showSuccess('Phone verified! Please complete your profile.');
          Future.delayed(Duration(milliseconds: 500), () {
            Get.offAllNamed(Pages.complete_info.value);
          });
        } else {
          // Verification failed
          _showError('Verification failed. Please try again.');
        }
      } else {
        _showError(' Please try again.');
      }
    } catch (e) {
      print(' Error handling verification success: $e');
    }
  }

  void _handleVerificationError(ResponseModel response) {
    String errorMsg = 'Invalid verification code. Please try again.';

    try {
      if (response.data is Map<String, dynamic>) {
        final errorData = response.data as Map<String, dynamic>;
        if (errorData['message'] != null) {
          errorMsg = errorData['message'].toString();
        }
      }
    } catch (e) {
      print(' Error parsing error response: $e');
    }

    _showError(errorMsg);
  }

  void goBack() {
    Get.back();
  }

  void onCodeChanged(String value) {
    if (errorMessage.value.isNotEmpty) {
      errorMessage.value = '';
    }
  }

  void _showSuccess(String message) {
    PopUpToast.show(message);
    Get.snackbar('Success!', message, backgroundColor: Colors.green, colorText: Colors.white);
  }

  void _showError(String message) {
    errorMessage.value = message;
    PopUpToast.show(message);
    Get.snackbar('Error', message, backgroundColor: Colors.red, colorText: Colors.white);
  }
}
