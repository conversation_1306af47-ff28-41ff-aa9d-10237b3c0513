import 'package:get/get.dart';
import 'package:renvo_app/core/models/category/sub_categories.dart';
import 'dart:developer';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/add/models/order_models.dart';

class AddOrderPage2Controller extends GetxController {
  RxInt selectedSubCategoryId = 0.obs;
  late final int id_cat;
  late final OrderDataModel order;
  // ObsVar <SubCategory> subCategory = ObsVar(null);
  // ObsVar<int> selectedSubCategoryId = ObsVar(0);
  ObsList<SubCategory> subCategory = ObsList([]);
  fetch() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.sub_categories(id_cat),
          fromJson: SubCategory.fromJson,
          // fromJson: (json) => SubCategory.fromJson(json['data']),
          // fromJson: (json) => (json['data'] as List)
          //     .map((e) => SubCategory.fromJson(e))
          //     .toList(),

          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );
    print(response.data); // أو print(response.toJson()); حسب الكلاس عندك

    if (response.success) {
      subCategory.value = response.data;
      // price.value = product.value!.totalPrice;
    } else {
      subCategory.error = response.message;
    }
  }

  @override
  onInit() {
    order = Get.arguments as OrderDataModel;
    id_cat = order.categoryId!;
    // id_cat = Get.arguments;
    // log(id_cat.toString());
    fetch();
    super.onInit();
  }
}
