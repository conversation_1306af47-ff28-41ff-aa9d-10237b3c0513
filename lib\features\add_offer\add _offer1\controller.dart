import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:renvo_app/core/routes/routes.dart';

class AddOffer1Controller extends GetxController {
  var price = ''.obs;
  var duration = ''.obs;
  var unit = 'hours'.obs; // القيمة الافتراضية
  // final id = Get.arguments;
  late final int id;

  // وحدات الوقت الثابتة
  final List<String> units = ['hours', 'days', 'months'];
  TextEditingController descController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  RxList<XFile> images = <XFile>[].obs;
  @override
  onInit() {
    id = Get.arguments;

    super.onInit();
  }

  // ميثود لإرسال العرض
  void sendOffer() {
    // ضع هنا منطق الإرسال للباك
    print(' price: ${price.value}');
    print('duration: ${duration.value}');
    print('unit: ${unit.value}');

    Get.toNamed(Pages.review_offer.value, arguments: {
      'price': price.value,
      'duration': duration.value,
      'unit': unit.value,
      'description': descController.text,
      'images': images,
      'id': id
    });
  }

  // يمكنك استخدام API هنا

  Future<void> pickImages() async {
    final picker = ImagePicker();
    final picked = await picker.pickMultiImage();
    if (picked != null && picked.isNotEmpty) {
      images.addAll(picked);
    }
  }
}
