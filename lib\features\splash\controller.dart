import 'package:get/get.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/routes/routes.dart';

class SplashScreenController extends GetxController {
  loadData() async {
    await 2.seconds.delay();
    // await Future.delayed(Duration(seconds: 2));
    await Get.find<AppBuilder>().init();
    // Get.toNamed(Pages.login.value);
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }
}
