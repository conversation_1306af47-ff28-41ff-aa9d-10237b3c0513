import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/gen/assets.gen.dart';

import 'controller.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LoginPageController());
    return Scaffold(
        // الكي يلي بدي حطو هون رح يوصلني ل ستيت الفورم مشان اوصل لبارمتراتا والفنكشنات الموجودين فيها بتحكم بكلشي فنكشنات موجودين بالستيت تبع الفورم
        //بوصللها  keyاذا رحنا عالفورم من جوا هوة ستيت فول ودجت وفيا ستيت فيا فنكشنات بحاجة أوصللها من ال
        //الكي هية شغلة بتلقطلي الودجت مارح نستخدمها بالعموم غير هون

        //   body: Form(
        //     key: controller.formKey,
        //     child: ListView(
        //       children: [
        //         SizedBox(height: MediaQuery.sizeOf(context).height * .2),
        //         TextFormField(
        //           controller: controller.email,
        //           validator: (value) {
        //             // بفعلو الفورم اتوماتك هي القيمة بترجع ك ايرور
        //             if (value!.isEmpty) {
        //               return "This field is required";
        //             }
        //             if (!value.contains("@")) {
        //               return "Wrong email";
        //             }
        //             return null;
        //           },
        //         ),
        //         SizedBox(height: 16),
        //         TextFormField(
        //           controller: controller.password,
        //         ),
        //         SizedBox(height: 16),
        //         ElevatedButton(
        //           // عليي اوصل للتابع اللي بنشط الفاليديشن
        //           onPressed: controller
        //               .confirm, // الاون بريس نوعا فنكشن أنا وقت قلو عطيتو كونترولر دوت كونفيرم فأنا عطيتو تعريف الفنكشن وليس استدعاء الفنكشن
        //           // هاد بيعني وقت اعطي تعريف بيجي ياخد هوة صلاحية يستدعي هاد الفنكشن عند الحاجة مثلا وقت اكبس
        //           // أما لما حط أقواس ف أنا اثناء البناء عملت استدعاء للفنكشن وهيك بتخرب الدنية لانو تنفذت عند البناء مو عند الضغط
        //           child: Text("confirm"),
        //         ),
        //         SizedBox(height: 16),
        //         TextButton(
        //           onPressed: () => Get.toNamed(Pages.register.value),
        //           child: Text("register"),
        //         ),
        //       ],
        //     ),
        //   ),
        // );
        body: Stack(
      // alignment: Alignment.bottomCenter,
      children: [
        Container(
          width: MediaQuery.sizeOf(context).width,
          height: MediaQuery.sizeOf(context).height * 0.25,
          // color: StyleRepo.deepBlue1,
          decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: [StyleRepo.deepBlueFegma, StyleRepo.deebBlue2])),
          child: Align(
            alignment: Alignment.center,
            child: Assets.icons.logoWhite.svg(width: 200, height: 100),
            // child: Assets.images.downloadInvisio.image(),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(36), color: Colors.white),
            height: MediaQuery.sizeOf(context).height * 0.80,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Padding(
                padding: EdgeInsets.all(30),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Welcome to ",
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 26,
                      ),
                    ),
                    Icon(Icons.favorite)
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 33),
                child: Text(
                  "Login to your account ",
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 26,
                  ),
                ),
              ),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 33),
                  child:
                      Text("Enter the following info to reach your account.")),
              Form(
                key: controller.formKey,
                child: Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 30),
                    child: ListView(children: [
                      Text("Phone Number"),
                      // SizedBox(height: MediaQuery.sizeOf(context).height * .2),
                      TextFormField(
                        controller: controller.number,
                        keyboardType: TextInputType.phone,
                        decoration: InputDecoration(
                          labelText: "Phone Number",
                          hintText: "+963 9xx xxx xxx",
                          prefixIcon: Icon(
                              Icons.phone), // ← الأيقونة على اليسار داخل الحقل
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        validator: (value) {
                          // بفعلو الفورم اتوماتك هي القيمة بترجع ك ايرور
                          if (value!.isEmpty) {
                            return "This field is required";
                          }

                          return null;
                        },
                      ),
                      SizedBox(height: 16),
                      TextFormField(
                        controller: controller.password,
                        decoration: InputDecoration(
                          labelText: "Password",
                          hintText: "****",

                          prefixIcon: Icon(Icons.lock_reset_rounded),
                          // ← الأيقونة على اليسار داخل الحقل
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      SizedBox(
                        width: MediaQuery.sizeOf(context).width * 0.2,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: controller.confirm,
                          // onPressed: () => Get.toNamed(Pages.verify.value),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: StyleRepo.deepBlueFegma,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                          child: const Text(
                            'Log In',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),

                      SizedBox(height: 16),
                      TextButton(
                        onPressed: () => Get.toNamed(Pages.register.value),
                        child: Text(
                          "Sign up",
                          style: TextStyle(color: Colors.green),
                        ),
                      )
                    ]),
                  ),
                ),
              )
            ]),
          ),
        )
      ],
    ));
  }
}

//       backgroundColor: Colors.white,
//       body: SingleChildScrollView(
//         child: Column(
//           children: [
//             // الجزء العلوي (الخلفية البنفسجية مع الشعار)
//             Container(
//               width: double.infinity,
//               height: 220,
//               decoration: const BoxDecoration(
//                 color: Color(0xFF4B00D1),
//                 borderRadius: BorderRadius.only(
//                   bottomLeft: Radius.circular(40),
//                   bottomRight: Radius.circular(40),
//                 ),
//               ),
//               child: const Center(
//                 child: SizedBox(
//                   height: 80,
//                   width: 80,
//                   child: DecoratedBox(
//                     decoration: BoxDecoration(
//                       color: Colors.grey,
//                       shape: BoxShape.rectangle,
//                     ),
//                   ),
//                 ),
//               ),
//             ),

//             const SizedBox(height: 24),

//             // العناوين
//             const Padding(
//               padding: EdgeInsets.symmetric(horizontal: 24.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     "Welcome to Renvo",
//                     style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                   ),
//                   SizedBox(height: 4),
//                   Row(
//                     children: [
//                       Text(
//                         "Login to your account.",
//                         style: TextStyle(
//                             fontSize: 18, fontWeight: FontWeight.bold),
//                       ),
//                       SizedBox(width: 6),
//                       Text("😊", style: TextStyle(fontSize: 18)),
//                     ],
//                   ),
//                   SizedBox(height: 8),
//                   Text(
//                     "Enter the following info to reach your account.",
//                     style: TextStyle(fontSize: 14, color: Colors.black54),
//                   ),
//                   SizedBox(height: 32),
//                 ],
//               ),
//             ),

//             // حقل رقم الهاتف
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24.0),
//               child: TextField(
//                 keyboardType: TextInputType.phone,
//                 decoration: InputDecoration(
//                   labelText: "Phone Number",
//                   hintText: "Ex: +999 123 456 789",
//                   prefixIcon: Icon(Icons.phone),
//                   border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12)),
//                 ),
//               ),
//             ),

//             const SizedBox(height: 16),

//             // حقل كلمة المرور
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24.0),
//               child: TextField(
//                 obscureText: true,
//                 decoration: InputDecoration(
//                   labelText: "Password",
//                   hintText: "123456!!",
//                   prefixIcon: Icon(Icons.lock),
//                   suffixIcon: Icon(Icons.visibility),
//                   border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(12)),
//                 ),
//               ),
//             ),

//             // رابط نسيت كلمة المرور
//             Padding(
//               padding: const EdgeInsets.only(right: 24.0, top: 8),
//               child: Align(
//                 alignment: Alignment.centerRight,
//                 child: Text(
//                   "Forget Passwords ?",
//                   style: TextStyle(fontSize: 13, color: Colors.black87),
//                 ),
//               ),
//             ),

//             const SizedBox(height: 24),

//             // زر تسجيل الدخول
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24.0),
//               child: SizedBox(
//                 width: double.infinity,
//                 height: 50,
//                 child: ElevatedButton(
//                   onPressed: () {},
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Color(0xFF4B00D1),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(25),
//                     ),
//                   ),
//                   child: const Text("Login", style: TextStyle(fontSize: 18)),
//                 ),
//               ),
//             ),

//             const SizedBox(height: 16),

//             // زر التسجيل
//             const Text(
//               "Sign up",
//               style: TextStyle(color: Colors.green, fontSize: 16),
//             ),

//             const SizedBox(height: 12),

//             // رابط الدخول كزائر
//             const Text(
//               "🔗 Join us as a guest",
//               style: TextStyle(
//                 color: Colors.blueAccent,
//                 decoration: TextDecoration.underline,
//                 fontSize: 14,
//               ),
//             ),

//             const SizedBox(height: 32),
//           ],
//         ),
//       ),
//     );
//   }
// }

    //   backgroundColor: Colors.white,
    //   body: Positioned.fill(
    //     top: 500,
    //     child: Stack(
    //       children: [
    //         // ✅ الخلفية العلوية البنفسجية
    //         Container(
    //           height: 250,
    //           width: double.infinity,
    //           decoration: const BoxDecoration(
    //             color: Color(0xFF4B00D1),
    //             borderRadius: BorderRadius.only(
    //               bottomLeft: Radius.circular(40),
    //               bottomRight: Radius.circular(40),
    //             ),
    //           ),
    //           child: const Center(
    //             child: SizedBox(
    //               height: 80,
    //               width: 80,
    //               child: DecoratedBox(
    //                 decoration: BoxDecoration(
    //                   color: Colors.grey,
    //                   shape: BoxShape.rectangle,
    //                 ),
    //               ),
    //             ),
    //           ),
    //         ),

    //         // ✅ المحتوى داخل Stack
    //         SingleChildScrollView(
    //           child: Padding(
    //             padding: const EdgeInsets.symmetric(horizontal: 24.0),
    //             child: Form(
    //               key: controller.formKey,
    //               child: Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //                   const Text(
    //                     "Welcome to Renvo",
    //                     style: TextStyle(
    //                         fontSize: 20, fontWeight: FontWeight.bold),
    //                   ),
    //                   const SizedBox(height: 4),
    //                   const Row(
    //                     children: [
    //                       Text(
    //                         "Login to your account.",
    //                         style: TextStyle(
    //                             fontSize: 18, fontWeight: FontWeight.bold),
    //                       ),
    //                       SizedBox(width: 6),
    //                       Text("😊", style: TextStyle(fontSize: 18)),
    //                     ],
    //                   ),
    //                   const SizedBox(height: 8),
    //                   const Text(
    //                     "Enter the following info to reach your account.",
    //                     style: TextStyle(fontSize: 14, color: Colors.black54),
    //                   ),
    //                   const SizedBox(height: 32),

    //                   // ✅ حقل رقم الهاتف
    //                   TextFormField(
    //                     controller: controller.email,
    //                     keyboardType: TextInputType.phone,
    //                     decoration: InputDecoration(
    //                       labelText: "Phone Number",
    //                       hintText: "Ex: +999 123 456 789",
    //                       prefixIcon: Icon(Icons.phone),
    //                       border: OutlineInputBorder(
    //                           borderRadius: BorderRadius.circular(12)),
    //                     ),
    //                     validator: (value) {
    //                       if (value == null || value.isEmpty) {
    //                         return 'Please enter your phone number';
    //                       }
    //                       return null;
    //                     },
    //                   ),

    //                   const SizedBox(height: 16),

    //                   // ✅ حقل كلمة المرور
    //                   TextFormField(
    //                     controller: controller.password,
    //                     obscureText: true, // غيّرها لاحقًا باستخدام GetX
    //                     decoration: InputDecoration(
    //                       labelText: "Password",
    //                       hintText: "123456!!",
    //                       prefixIcon: Icon(Icons.lock),
    //                       suffixIcon:
    //                           Icon(Icons.visibility), // اجعلها Obx لاحقًا
    //                       border: OutlineInputBorder(
    //                           borderRadius: BorderRadius.circular(12)),
    //                     ),
    //                     validator: (value) {
    //                       if (value == null || value.isEmpty) {
    //                         return 'Please enter your password';
    //                       }
    //                       return null;
    //                     },
    //                   ),

    //                   const SizedBox(height: 8),
    //                   Align(
    //                     alignment: Alignment.centerRight,
    //                     child: Text(
    //                       "Forget Passwords ?",
    //                       style: TextStyle(fontSize: 13, color: Colors.black87),
    //                     ),
    //                   ),

    //                   const SizedBox(height: 24),

    //                   // ✅ زر الدخول
    //                   SizedBox(
    //                     width: double.infinity,
    //                     height: 50,
    //                     child: ElevatedButton(
    //                       onPressed: () {
    //                         // if (formKey.currentState!.validate()) {
    //                         // ✅ هنا يتم الإرسال أو الانتقال أو التخزين
    //                         print("Phone: ");
    //                         print("Password:");
    //                         // }
    //                       },
    //                       style: ElevatedButton.styleFrom(
    //                         backgroundColor: Color(0xFF4B00D1),
    //                         shape: RoundedRectangleBorder(
    //                           borderRadius: BorderRadius.circular(25),
    //                         ),
    //                       ),
    //                       child: const Text("Login",
    //                           style: TextStyle(fontSize: 18)),
    //                     ),
    //                   ),

    //                   const SizedBox(height: 16),
    //                   const Center(
    //                     child: Text(
    //                       "Sign up",
    //                       style: TextStyle(color: Colors.green, fontSize: 16),
    //                     ),
    //                   ),

    //                   const SizedBox(height: 12),
    //                   const Center(
    //                     child: Text(
    //                       "🔗 Join us as a guest",
    //                       style: TextStyle(
    //                         color: Colors.blueAccent,
    //                         decoration: TextDecoration.underline,
    //                         fontSize: 14,
    //                       ),
    //                     ),
    //                   ),

    //                   const SizedBox(height: 40),
    //                 ],
    //               ),
    //             ),
    //           ),
    //         ),
    //       ],
    //     ),
//     //   ),
//     );

//   }
// }
