class ProviderCategory {
  final int id;
  final String title;
  final int providerCount;
  final List<String> keywords;
  final String imageUrl;
  final String svg;

  ProviderCategory({
    required this.id,
    required this.title,
    required this.providerCount,
    required this.keywords,
    required this.imageUrl,
    required this.svg,
  });

  factory ProviderCategory.fromJson(Map<String, dynamic> json) {
    print(json);
    return ProviderCategory(
      id: json['id'],
      title: json['title'],
      providerCount: json['prv_cnt'],
      keywords: (json['keywords'] as List<dynamic>?)
              ?.map((k) => k['title'].toString())
              .toList() ??
          [],
      imageUrl: json['banner']?['original_url'] ?? '',
      svg: json['svg'],
    );
  }
}
