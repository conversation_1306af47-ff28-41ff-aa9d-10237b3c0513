// import 'package:renvo_app/features/my_order/controller.dart';

// class MyOrderModel {
//   final int id;
//   final String title;
//   final OrderStatus status;

//   MyOrderModel({
//     required this.id,
//     required this.title,
//     required this.status,
//   });
// }

class MyOrderModel {
  final int id;
  final String mainCategoryTitle;
  final String subCategoryTitle;
  final String description;
  final String location;
  final String proName;
  final String CustomerName;

  MyOrderModel({
    required this.id,
    required this.mainCategoryTitle,
    required this.subCategoryTitle,
    required this.description,
    required this.location,
    required this.proName,
    required this.CustomerName,
  });

  factory MyOrderModel.fromJson(Map<String, dynamic> json) {
    return MyOrderModel(
      CustomerName: json['customer']?['first_name'] ?? '',
      id: json['id'] ?? 0,
      mainCategoryTitle: json['mainCategory']?['title'] ?? '',
      subCategoryTitle: json['category']?['title'] ?? '',
      description: json['description'] ?? '',
      location: json['address']?['title'] ?? '',
      proName: json['provider']?['name'] ?? '',
    );
  }
  @override
  String toString() {
    return 'Offer(id: $id, desc: $description,)';
  }
}
