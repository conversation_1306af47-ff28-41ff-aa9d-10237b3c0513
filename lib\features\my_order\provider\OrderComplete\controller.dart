import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderCompleteProController extends GetxController {
  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.provider_orders,
        params: {"page": page, "status": "completed"},
        cancelToken: cancel,
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
