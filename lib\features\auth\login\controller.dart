import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/config/role.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/config/app_builder.dart';

import 'package:renvo_app/core/services/rest_api/rest_api.dart';
// الاب بيلدر المفروض
// من الغت بروفايل بشوف هل اليوزر بقلبو بروفايدر ولا لاء اذا في بقلبو بروفايدر بروح بشيك عالاب بيلدر انو هل أنا عم بتصفح ك بروفايدر ولا ك يوزر
// هاد الستيت يلي بالاب بيلدر مابيسوى شي اذالا اليوزر مافي بقلبو بروفايدر
//لما تجيني البيانات من الغت بروفايل بدي حط الاسم مثلا منين بدي جيبو بجي بقول اذا كان الاب بيلدر  إز بروفايدر ف عطيني الاسم من البروفايدر اللي بقلب اليوزر اذا كان الاب بيلدر مانو بروفايدر
//عطيني الاسم من اليوزر
//موضوع الستيت صار مع الاز بروفايدر بالاب بيلدر بجي انا بعمل عالناف بار او بي اكس يقرأ من الاز
//بدنا نساوي الغت بروفايل بعدين نفوت على شرط تبع الاب بيلدر إز بروفايدر
// class LoginPageController extends GetxController {
//   GlobalKey<FormState> formKey = GlobalKey<FormState>();

//   late TextEditingController number, password;

//   @override
//   onInit() {
//     number = TextEditingController();
//     password = TextEditingController();
//     super.onInit();
//   }

//   @override
//   onClose() {
//     number.dispose();
//     password.dispose();
//     super.onClose();
//   }

//   confirm() async {
//     // هاد التابع اللي بنشط الفالديشن
//     if (!formKey.currentState!.validate()) {
//       // null اشارة التعجب اذا ماشتغل الفاليديشن  والتعجب التانية انو على كفالتي مالها
//       // هي لحالا بتخليني خلي الفورم يستدعي كلشي فيلدات بقلبو يشوف فيها تابع الفاليديتر بفعل الفاليديتر عند كل فيلد يا بينجح يا بيفشل
//       // فالديت بوليان اذا فيلد واحد فشل فبيعطيني فولس
//       return;
//     } else {
//       // Get.toNamed(Pages.verify.value); هي للانتقال الوهمي
//     }
//     // http://94.72.98.154/renva/public/
//     // http://94.72.98.154/renva/public/api/v1/login

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late TextEditingController number, password;

  @override
  onInit() {
    // Get.put(APIService());

    number = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    number.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    //  هاد التابع اللي بنشط الفالديشن

    // null اشارة التعجب اذا ماشتغل الفاليديشن  والتعجب التانية انو على كفالتي مالها
    // هي لحالا بتخليني خلي الفورم يستدعي كلشي فيلدات بقلبو يشوف فيها تابع الفاليديتر بفعل الفاليديتر عند كل فيلد يا بينجح يا بيفشل
    // فالديت بوليان اذا فيلد واحد فشل فبيعطيني فولس
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.login,
          method: RequestMethod.Post,
          // params: {"success": true},
          body: {
            "phone": number.text,
            "password": password.text,
            "dial_country_code": "963",
            "device_token": "DEVICE_TOKEN"
          },
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );
    if (response.success) {
      appBuilder.setRole(Role.user);

      appBuilder.setToken(response.data['token']);
      final userData = response.data['data'];
      // Get.find<AppBuilder>().saveUserDataFromApi(userData);

      Get.offAllNamed(Pages.home.value);
    } else {} // Get.toNamed(Pages.verify.value); هي للانتقال الوهمي
  }
}
//     if (response.success) {
//       appBuilder.setRole(Role.user);

//       // تحقق من وجود التوكن في الرد
//       if (response.data != null && response.data['token'] != null) {
//         appBuilder.setToken(response.data['token']);
//       }

//       // تحقق من وجود بيانات المستخدم
//       if (response.data != null && response.data['data'] != null) {
//         final userData = response.data['data'] as Map<String, dynamic>;
//         appBuilder.saveUserDataFromApi(userData);

//         Get.offAllNamed(Pages.home.value);
//       } else {
//         // لم تصل بيانات المستخدم، أظهر رسالة خطأ
//         Get.snackbar("خطأ", "فشل تسجيل الدخول: لم يتم استقبال بيانات المستخدم");
//       }
//     } else {
//       // فشل المصادقة أو خطأ آخر
//       Get.snackbar("فشل تسجيل الدخول", response.message ?? "حصل خطأ غير متوقع");
//     }
//   }
// }
