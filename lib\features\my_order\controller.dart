import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/my_order/models/my_offer.dart';
import 'package:renvo_app/features/my_order/models/view_offer.dart';

enum OrderStatus { waiting, processing, completed, cancelled }

// class MyOrderPageController extends GetxController with GetSingleTickerProviderStateMixin {
//   late TabController tabController;
//   // المتغير الذي يحمل الحالة المختارة من التاب
//   var selectedStatus = OrderStatus.pending.obs;

//   @override
//   void onInit() {
//     super.onInit();
//     tabController = TabController(length: 4, vsync: this);

//     // كلما تغير التاب، نغير قيمة selectedStatus
//     tabController.addListener(() {
//       selectedStatus.value = OrderStatus.values[tabController.index];
//       // هنا ممكن ترسل api مع selectedStatus
//       fetchOrdersForStatus(selectedStatus.value);
//     });
//   }

//   void fetchOrdersForStatus(OrderStatus status) {
//     // هنا أرسل الطلب للباك باستخدام status
//     // print(status);
//     // مثال:
//     // APIService.getOrdersByStatus(status.name);
//   }

//   @override
//   void onClose() {
//     tabController.dispose();
//     super.onClose();
//   }
// }

class MyOrderPageController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  var selectedStatus = OrderStatus.waiting.obs;
  // ObsList<VOfferModel> offers = ObsList([]);
  late PaginationController pagerController;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);

    tabController.addListener(() {
      selectedStatus.value = OrderStatus.values[tabController.index];
    });
  }

  // Future<ResponseModel> Function(int, CancelToken) viewOffer(int id) {
  //   return (int page, CancelToken cancel) async {
  //     final response = await APIService.instance.request(
  //       Request(
  //         endPoint: EndPoints.order_offers(id),
  //         params: {
  //           "page": page,
  //         },
  //         cancelToken: cancel,
  //         copyHeader: {
  //           'Accept': 'application/json',
  //           'Content-Type': 'application/json'
  //         },
  //       ),
  //     );
  //     return response;
  //   };
  // }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
