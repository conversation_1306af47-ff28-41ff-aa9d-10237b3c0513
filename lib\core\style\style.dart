import 'package:flutter/material.dart';
import 'package:renvo_app/core/style/repo.dart';

class AppStyle {
  static ThemeData get theme {
    return ThemeData(
      primaryColor: StyleRepo.green,
      scaffoldBackgroundColor: StyleRepo.white,
      navigationBarTheme: NavigationBarThemeData(
        iconTheme: WidgetStateProperty.resolveWith(
          (states) {
            if (states.contains(WidgetState.selected)) {
              return IconThemeData(color: StyleRepo.deepBlueFegma);
            }
            return IconThemeData(color: StyleRepo.darkGrey);
          },
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(45),
        ),
      ),
    );
  }
}
