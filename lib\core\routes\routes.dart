// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/add/addorder2/index.dart';
import 'package:renvo_app/features/add/addorder3/index.dart';
import 'package:renvo_app/features/add/addorder4/index.dart';
import 'package:renvo_app/features/add/addorderfinal/index.dart';
import 'package:renvo_app/features/add_offer/add%20_offer1/index.dart';
import 'package:renvo_app/features/add_offer/add_offer2/index.dart';
import 'package:renvo_app/features/auth/Verify/index.dart';
import 'package:renvo_app/features/home/<USER>/index.dart';
import 'package:renvo_app/features/join_provider/index.dart';
import 'package:renvo_app/features/my_order/customer/view_offer/index.dart';
import 'package:renvo_app/features/my_order/customer/view_offer/view_offers.dart';
import '../../features/auth/register/index.dart';
import '../../features/main/index.dart';

import '../../features/auth/login/index.dart';

import '../../features/splash/index.dart';

class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: "/unknown", page: () => SizedBox());

  static GetPage initialRoute = GetPage(
    name: "/",
    page: () => SplashScreen(),
  );

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  //Auth
  login,
  register,
  verify,
  //
  home,
  Join_Provider,
  add_order2,
  add_order3,
  add_order4,
  order_summary,
  order_details,
  add_offer1,
  review_offer,
  view_offer
  // product_details,
  // products,
  //
  ;

  String get value => '/$name';

  GetPage get page => switch (this) {
        login => GetPage(
            name: value,
            page: () => LoginPage(),
          ),
        verify => GetPage(
            name: value,
            page: () => VerifyPage(),
          ),
        register => GetPage(
            name: value,
            page: () => RegisterPage(),
          ),
        home => GetPage(
            name: value,
            page: () => MainPage(),
          ),
        Join_Provider => GetPage(
            name: value,
            page: () => JoinProviderPage(),
          ),
        add_order2 => GetPage(
            name: value,
            page: () => AddOrderPage2(),
          ),
        add_order3 => GetPage(
            name: value,
            page: () => AddOrderPage3(),
          ),
        add_order4 => GetPage(
            name: value,
            page: () => AddOrderPage4(),
          ),
        order_summary => GetPage(
            name: value,
            page: () => OrderSummaryPage(),
          ),
        order_details => GetPage(
            name: value,
            page: () => OrderDetailsPage(),
          ),
        add_offer1 => GetPage(
            name: value,
            page: () => AddOfferPage1(),
          ),
        review_offer => GetPage(
            name: value,
            page: () => ReviewOfferPage(),
          ),
        view_offer => GetPage(
            name: value,
            page: () => ViewOffersPage(),
          ),
        // product_details => GetPage(
        //     name: value,
        //     page: () => ProductDetailsPage(),
        //   ),
        // products => GetPage(
        //     name: value,
        //     page: () => ProductsPage(),
        //   ),
      };
}
