// class JoinProPage1 extends StatelessWidget {
//   const JoinProPage1({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/join_provider/jo_pro/controller.dart';

class JoinProPage1 extends StatelessWidget {
  // final ProviderController controller = Get.put(ProviderController());
  final ProviderController1 controller = Get.put(ProviderController1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Join as a service provider"),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Name
            TextField(
              controller: controller.nameController,
              decoration: InputDecoration(
                labelText: "Name",
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16),

            // Email
            TextField(
              controller: controller.emailController,
              decoration: InputDecoration(
                labelText: "Email",
                prefixIcon: Icon(Icons.email),
              ),
            ),
            SizedBox(height: 16),

            // Phone
            TextField(
              controller: controller.phoneController,
              decoration: InputDecoration(
                labelText: "Phone number",
                prefixIcon: Icon(Icons.phone),
              ),
            ),
            SizedBox(height: 16),

            // Gender
            Obx(() => DropdownButtonFormField<String>(
                  value: controller.selectedGender.value,
                  items: ["Male", "Female"]
                      .map((gender) =>
                          DropdownMenuItem(value: gender, child: Text(gender)))
                      .toList(),
                  onChanged: (val) {
                    controller.selectedGender.value = val!;
                  },
                  decoration: InputDecoration(
                    labelText: "Gender",
                    prefixIcon: Icon(Icons.male),
                  ),
                )),
            SizedBox(height: 16),

            // Working time
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(labelText: "From"),
                    onChanged: (val) => controller.workFrom.value = val,
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(labelText: "To"),
                    onChanged: (val) => controller.workTo.value = val,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Description
            TextField(
              controller: controller.descriptionController,
              maxLines: 4,
              decoration: InputDecoration(
                labelText: "Description",
                alignLabelWithHint: true,
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 24),

            // Submit button
            Center(
              child: ElevatedButton(
                onPressed: controller.registerProvider,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30)),
                  padding: EdgeInsets.symmetric(horizontal: 60, vertical: 14),
                ),
                child: Text("Next"),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
