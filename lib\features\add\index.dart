import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:renvo_app/core/models/category/all_categories.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/features/add/controller.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'package:renvo_app/gen/assets.gen.dart';
import 'package:get/get.dart';

class AddPage extends StatelessWidget {
  const AddPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddPageController());
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              StyleRepo.deepBlueFegma,
              StyleRepo.deepBlue,
              StyleRepo.deebBlue2,
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
                padding: const EdgeInsets.all(16),
                child: Assets.icons.logoAddorder.svg()),

            const SizedBox(height: 20),

            // العنوان
            const Text(
              "Add Order",
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 10),

            // الوصف
            const Text(
              "Select Services type to complete order",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 30),

            // البطاقات الأربع
            Expanded(
                child: ObsListBuilder(
              obs: controller.categories,
              builder: (context, categories) {
                if (categories.isEmpty) {
                  return Center(child: Text("No categories found."));
                }
                return Padding(
                    padding: const EdgeInsets.only(
                        bottom: 18.0, right: 12, left: 12),
                    // child: ClipRRect(
                    //   borderRadius: BorderRadius.circular(20),
                    //   child: BackdropFilter(
                    //       filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                    //       child: Container(
                    //           padding: const EdgeInsets.symmetric(
                    //               vertical: 1, horizontal: 16),
                    //           decoration: BoxDecoration(
                    //             color: const Color.fromARGB(255, 207, 206, 206)
                    //                 .withOpacity(0.10),
                    //             borderRadius: BorderRadius.circular(20),
                    //             // border: Border.all(
                    //             //     color: Colors.white.withOpacity(0.23),
                    //             //     width: 2),
                    //           ),
                    child: ListView.separated(
                      itemCount: categories.length,
                      separatorBuilder: (_, __) => SizedBox(height: 3),
                      itemBuilder: (context, index) {
                        final category = categories[index];
                        print('rtf ${category.svg}');

                        return InkWell(
                            // onTap: () =>   Get.toNamed(Pages.register.value) ,
                            // child: servicecard(category));
                            child: _buildServiceCard(
                                id_cat: category.id,
                                // category: category,
                                icon: Icons.home_filled,
                                title: category.title,
                                subtitle:
                                    "Providers: ${category.providerCount}",
                                Keywords:
                                    " Keywords: ${category.keywords.join(", ")}"
                                // color: Colors.blueAccent,
                                ));
                      },
                    )
                    // )
                    // ),
                    // ),
                    );
              },
            ))
          ],
        ),
      ),
    );
  }
}
//  _buildServiceCard(
//   icon: Icons.home_filled,
//   title: "Household Services",
//   subtitle: "Cleaning, Ironing & Washing",
//   // color: Colors.blueAccent,
// ),
// _buildServiceCard(
//   icon: Icons.build,
//   title: "Professional Services",
//   subtitle: "Electrical, Plumbing, Painting",
//   color: Colors.lightBlue,
// ),
// _buildServiceCard(
//   icon: Icons.person,
//   title: "Personal Services",
//   subtitle: "Personal Training, Tutoring",
//   color: Colors.deepPurpleAccent,
// ),
// _buildServiceCard(
//   icon: Icons.local_shipping,
//   title: "Logistical Services",
//   subtitle: "Transport, Deliveries, Packing",
//   color: Colors.deepPurple,
// ),

Widget _buildServiceCard({
  required IconData icon,
  required String title,
  required int id_cat,
  required String subtitle,
  required String Keywords,
  // required AllCategory category,
  // required Color color,
}) {
  return InkWell(
    onTap: () {
      final order = OrderDataModel(categoryId: id_cat);
      Get.toNamed(Pages.add_order2.value, arguments: order);
      //  Get.toNamed(Pages.add_order2.value, arguments: id_cat),
    },
    child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 207, 206, 206)
                  .withValues(alpha: 0.10),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Icon(icon, size: 40, color: Colors.white),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(title,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                            color: Colors.white70, fontSize: 12),
                      ),
                      Text(
                        Keywords,
                        style: const TextStyle(
                            color: Colors.white70, fontSize: 12),
                      ),
                      const SizedBox(height: 2),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_forward_ios, color: Colors.white),
              ],
            ),
          ),
        )),
  );
}
