import 'package:get/get.dart';

import 'dart:developer';

import 'package:get/get.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';

import 'models/profile.dart';

class ProfilePageController extends GetxController {
  ObsVar<ProfileModel> profile = ObsVar(null);

  fetch() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.get_profile,
          method: RequestMethod.Get,
          fromJson: ProfileModel.fromJson,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );

    if (response.success) {
      profile.value = response.data;
    } else {
      profile.error = response.message;
    }
  }

  @override
  onInit() {
    fetch();
    super.onInit();
  }
}
