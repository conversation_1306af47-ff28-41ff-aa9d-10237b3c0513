  Future<void> acceptOffer(String offerId) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.accept_offers,
      method: RequestMethod.Post,
      
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );
    return response;
  }









  import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/customer/OrderComplete/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderCardCompleteCustomer extends StatelessWidget {
  final MyOrderModel order;
  const OrderCardCompleteCustomer(this.order, {super.key});

  @override
  Widget build(BuildContext context) {
    // final controller = Get.put(SuggestionsPageController());
    // final MyOrderPageController controller = Get.put(MyOrderPageController());
    // final controller = Get.find<MyOrderPageController>();
    final controller = Get.find<OrderCompleteController>();
    final review = controller.reviews[1];
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          border: Border.symmetric(
            vertical: BorderSide(
                color: Colors.grey.shade300, width: 5), // top & bottom
            horizontal: BorderSide(
                color: Colors.grey.shade300, width: 6), // left & right
          ),
          // اللون الخارجي (الرمادي)
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.white, // اللون الداخلي للكارد
            borderRadius: BorderRadius.circular(16), // منحني داخلي
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// شريط العنوان: ID + الوقت + 3 نقاط
              Row(
                children: [
                  // شوفي هي كيف

                  Text(
                    // "iddd",
                    "ID  ${order.id}",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                  const Spacer(),
                  Text(
                    "date ",
                    // formatDate(suggestion.timestamp), // فقط اليوم والشهر والسنة
                    style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                  ),

                  // Text(
                  //   "${suggestion.timestamp}",
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: Colors.grey.shade600,
                  //   ),
                  // ),
                  const SizedBox(width: 1),
                  IconButton(
                    onPressed: () {
                      // AppDialogs.showDeleteConfirmation(
                      //   context: context,
                      //   onDelete: () async {
                      //     await controller.deleteSuggestion(
                      //         suggestion.id, context);
                      //   },
                      // );
                    },
                    icon: Icon(Icons.more_horiz),
                    color: Colors.grey,
                    iconSize: 18,
                  ),
                ],
              ),
              const SizedBox(
                  height: 1), // هون بكون المسافة بين العنوان واللي فوقا

              /// فئة الخدمة + الرمز
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      // color: Color.fromARGB(255, 116, 140, 169),
                      color: StyleRepo.deepBlue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.home_work_outlined,
                        color: Colors.white, size: 22),
                  ),
                  const SizedBox(width: 7),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        // "title",
                        "${order.mainCategoryTitle}",
                        // "${order.description.split(' ').take(4).join(' ') + (order.description.split(' ').length > 4 ? '...' : '')}",
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
                      Text(
                        // "user id",
                        "${order.subCategoryTitle}",
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  )
                ],
              ),
              const SizedBox(height: 1),
              Divider(color: Colors.grey.shade300),

              /// الوصف
              Text(
                // "descr",
                "${order.description}",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade900,
                ),
              ),
              const SizedBox(height: 4),
              Divider(color: Colors.grey.shade300),
              // const SizedBox(height: 6),
              // Spacer(),

              /// الموقع
              Row(
                children: [
                  const Icon(Icons.location_on, size: 20, color: Colors.grey),
                  Text("${order.location}"),
                ],
              ),
              const SizedBox(height: 1),
              Divider(),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    "Rating & Review",
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  // Spacer(),
                  Text(order.proName)
                ],
              ),
              Row(
                children: [
                  // ignore: unnecessary_null_comparison
                  if (controller.reviews != null) ...[
                    RatingBarIndicator(
                      rating: review?["rate"],
                      itemBuilder: (context, _) =>
                          const Icon(Icons.star, color: Colors.amber),
                      itemCount: 5,
                      itemSize: 24,
                    ),
                    const SizedBox(height: 5),
                    Text(review?["review"]),
                  ] else
                    // إذا ما فيه تقييم → اعرض زر "اضغط للتقييم"
                    // ElevatedButton(
                    //   onPressed: () {
                    //     _openReviewBottomSheet(orderId);
                    //   },
                    //   child: const Text("اضغط للتقييم"),
                    // ),
                    // ],
                    TextButton(
                        onPressed: () {
                          _openReviewBottomSheet(order.id);
                        },
                        child: Text(
                          " Tap to rating please",
                          // "Public: ${suggestion.isPublic}",
                          style: TextStyle(
                            fontSize: 13.5,
                            color: Colors.green,
                          ),
                        )),
                ],
              )
            ],
          ),
        ));
  }
}

void _openReviewBottomSheet(int orderId) {
  final c = Get.find<OrderCompleteController>();
  double rate = 3;
  final TextEditingController reviewController = TextEditingController();

  Get.bottomSheet(
    Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text("قيّم الطلب", style: TextStyle(fontSize: 18)),
          const SizedBox(height: 15),

          // النجوم
          RatingBar.builder(
            initialRating: 3,
            minRating: 1,
            allowHalfRating: true,
            itemCount: 5,
            itemSize: 32,
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (value) {
              rate = value;
            },
          ),

          const SizedBox(height: 15),

          // حقل النص
          TextField(
            controller: reviewController,
            decoration: const InputDecoration(
              hintText: "أدخل تعليقك",
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 15),

          // زر إرسال
          ElevatedButton(
            onPressed: () {
              c.submitReview(
                orderId,
                rate,
                reviewController.text,
              );
            },
            child: const Text("إرسال"),
          ),
        ],
      ),
    ),
  );
}
