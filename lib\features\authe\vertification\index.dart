import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:renva0/core/style/repo.dart';

import '../../../core/localization/strings.dart';
import '../../../core/widgets/auth_container.dart';
import '../../../gen/assets.gen.dart';
import 'controller.dart';

class VerifyPhonePage extends StatelessWidget {
  final String? phoneNumber;

  const VerifyPhonePage({super.key, this.phoneNumber});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyPhoneController(initialPhoneNumber: phoneNumber));

    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: [StyleRepo.deepBlue, Color(0xff0048D9)],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              _buildBackButton(theme, controller),
              _buildLogo(size),
              _buildMainContainer(context, controller, theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton(ThemeData theme, VerifyPhoneController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          GestureDetector(
            onTap: controller.goBack,
            child: Assets.icons.arrows.leftCircle.svg(
              width: 24,
              height: 24,
              colorFilter: const ColorFilter.mode(StyleRepo.softWhite, BlendMode.srcIn),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            tr(LocaleKeys.common_back),
            style: theme.textTheme.titleSmall?.copyWith(color: StyleRepo.softWhite),
          ),
        ],
      ),
    );
  }

  Widget _buildLogo(Size size) {
    return SizedBox(
      height: size.height * 0.18,
      child: Center(
        child: Assets.images.logo.logo.svg(
          width: 90,
          height: 90,
          colorFilter: const ColorFilter.mode(StyleRepo.softWhite, BlendMode.srcIn),
        ),
      ),
    );
  }

  Widget _buildMainContainer(
    BuildContext context,
    VerifyPhoneController controller,
    ThemeData theme,
  ) {
    return Expanded(
      child: AuthContainer(
        showWatermark: true,
        watermarkColor: StyleRepo.deepBlue,
        addScrolling: true,
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),

              _buildTitle(theme),

              const SizedBox(height: 8),

              Row(
                children: [
                  Expanded(child: _buildInstructionText(theme)),
                  const SizedBox(width: 12),
                  _buildPhoneNumberDisplay(controller, theme),
                ],
              ),

              const SizedBox(height: 30),

              _buildPinInput(controller, theme),

              const SizedBox(height: 60),

              _buildConfirmButton(controller, theme),

              const SizedBox(height: 20),

              Center(
                child: Text(
                  'Didn\'t receive the code? Check your messages or try again later.',
                  style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(ThemeData theme) {
    return Row(
      children: [
        Text(
          tr(LocaleKeys.verification_title),
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: StyleRepo.black,
          ),
        ),
        const SizedBox(width: 8),
        Assets.icons.document.check.svg(
          width: 24,
          height: 24,
          colorFilter: const ColorFilter.mode(StyleRepo.forestGreen, BlendMode.srcIn),
        ),
      ],
    );
  }

  Widget _buildInstructionText(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, top: 8.0),
      child: Text(
        tr(LocaleKeys.verification_subtitle),
        style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
      ),
    );
  }

  Widget _buildPhoneNumberDisplay(VerifyPhoneController controller, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Center(
        child: Text(
          '**** *** **45',
          textAlign: TextAlign.center,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: StyleRepo.black,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildPinInput(VerifyPhoneController controller, ThemeData theme) {
    final defaultStyle = PinTheme(
      width: 60,
      height: 60,
      textStyle: theme.textTheme.headlineSmall?.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: StyleRepo.black,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: StyleRepo.softGrey),
        borderRadius: BorderRadius.circular(16),
      ),
    );

    final focusedStyle = defaultStyle.copyDecorationWith(
      border: Border.all(color: StyleRepo.deepBlue, width: 2),
    );

    final filledStyle = defaultStyle.copyDecorationWith(
      color: StyleRepo.softGreen,
      border: Border.all(color: StyleRepo.forestGreen),
    );

    final errorStyle = defaultStyle.copyDecorationWith(
      border: Border.all(color: Colors.red, width: 2),
    );

    return Center(
      child: Obx(() {
        final hasError = controller.errorMessage.value.isNotEmpty;

        return Pinput(
          length: 4,
          controller: controller.codeController,
          validator: controller.validateVerificationCode,
          defaultPinTheme: hasError ? errorStyle : defaultStyle,
          focusedPinTheme: focusedStyle,
          submittedPinTheme: filledStyle,
          errorPinTheme: errorStyle,
          pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
          showCursor: true,
          onCompleted: (_) => controller.verifyPhoneNumber(),
          onChanged: controller.onCodeChanged,
        );
      }),
    );
  }

  Widget _buildConfirmButton(VerifyPhoneController controller, ThemeData theme) {
    return Obx(
      () => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: controller.isLoading.value ? null : controller.verifyPhoneNumber,
          child:
              controller.isLoading.value
                  ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: StyleRepo.softWhite,
                          strokeWidth: 2,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text('Verifying...'),
                    ],
                  )
                  : Text(tr(LocaleKeys.common_confirm)),
        ),
      ),
    );
  }
}
