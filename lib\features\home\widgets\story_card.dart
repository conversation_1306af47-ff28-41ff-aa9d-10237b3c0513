import 'package:flutter/material.dart';
import 'package:renvo_app/core/demo/media.dart';
import 'package:renvo_app/core/widgets/image.dart';

class StoryCard extends StatelessWidget {
  StoryCard({
    super.key,
    required this.story,
  });
  final path1 = DemoMedia.getRandomImage;
  final story;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Container(
        width: 160,
        height: 180,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.grey.shade200),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                onTap: () {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) {
                      // مدة العرض
                      const duration = Duration(seconds: 5);

                      // بداية عرض الستوري
                      Future.delayed(duration, () {
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        }
                      });

                      return Dialog(
                        // backgroundColor: Colors.black,
                        insetPadding: EdgeInsets.zero,
                        child: Expanded(
                          child: Stack(
                            children: [
                              // الصورة
                              Center(
                                child: Image.network(
                                  path1,
                                  fit: BoxFit.contain,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              ),
                              // الشريط العلوي
                              Positioned(
                                top: 20,
                                left: 20,
                                right: 20,
                                child: TweenAnimationBuilder<double>(
                                  tween: Tween(begin: 0.0, end: 1.0),
                                  duration: duration,
                                  builder: (context, value, child) {
                                    return LinearProgressIndicator(
                                      value: value,
                                      backgroundColor: Colors.white24,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
                child: AppImage(
                  path: path1,
                  type: ImageType.CachedNetwork,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 40,
                        child: LinearProgressIndicator(
                            value: 0.5, // Example progress
                            backgroundColor: Colors.grey.shade300,
                            color: Colors.white),
                      ),
                      Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(50)),
                          child: story.logoUrl != ''
                              ? AppImage(
                                  path: DemoMedia.getRandomImage,
                                  type: ImageType.CachedNetwork,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                )
                              : SizedBox())
                    ],
                  ),
                  const SizedBox(
                    height: 180,
                  ),
                  SizedBox(
                      width: double.infinity,
                      child: Text(
                        story.title,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 15),
                      ))
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
