import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/features/add/models/order_models.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';

class OrderSummaryController extends GetxController {
  late final OrderDataModel order;

  Null addressId = null;

  sendOrderToBackend(OrderDataModel order) async {
    // تجهيز الصور
    List<File> files = [];
    if (order.images != null) {
      files = order.images!.map((path) => File(path)).toList();
    }

    String typeValue =
        order.serviceType == 0 ? 'immediately' : 'none_immediately';

    FormData formData = FormData();

    // الحقول النصية
    formData.fields
      ..add(MapEntry('min_price', order.minPrice?.toString() ?? ''))
      ..add(MapEntry('max_price', order.maxPrice?.toString() ?? ''))
      ..add(MapEntry('description', order.description ?? ''))
      ..add(MapEntry(
          'date',
          order.selectedDate != null
              ? "${order.selectedDate!.year.toString().padLeft(4, '0')}-"
                  "${order.selectedDate!.month.toString().padLeft(2, '0')}-"
                  "${order.selectedDate!.day.toString().padLeft(2, '0')}"
              : '2025-11-11'))
      ..add(MapEntry('type', typeValue))
      ..add(MapEntry('address_id', ''))
      ..add(MapEntry('address_lat', "36.9999"))
      ..add(MapEntry('address_long', "24.5555"))
      ..add(MapEntry('address_title', "test"))
      ..add(MapEntry('prv_category_id', order.subCategoryId?.toString() ?? ''))
      ..add(MapEntry('main_category_id', order.categoryId?.toString() ?? ''));

    // الوقت إذا موجود
    if (order.selectedTime != null) {
      formData.fields.add(MapEntry('start_at', order.selectedTime!));
    }

    // الصور (gallery[0], gallery[1], ...)
    for (int i = 0; i < files.length; i++) {
      formData.files.add(MapEntry(
        'gallery[$i]',
        await MultipartFile.fromFile(files[i].path,
            filename: files[i].path.split('/').last),
      ));
    }
    final response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.orders,
          method: RequestMethod.Post,
          body: formData,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
    );

    if (response.success && response.data != null) {
      final rawList = response.data;
      print("sssssssssssssssssssssssssssss${rawList}");
      Get.until(
        (route) => route.settings.name == Pages.home.value,
      );
      // categories.value = rawList.map((e) => AllCategory.fromJson(e)).toList();
    } else {
      print(response.message ?? "Failed to load categories");
    }
    // الإرسال

    // يمكنك التعامل مع الاستجابة هنا
    print(response.data);
  }
}
