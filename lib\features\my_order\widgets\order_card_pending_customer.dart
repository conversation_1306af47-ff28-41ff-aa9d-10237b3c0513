import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/customer/OrderPending/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';
import 'package:renvo_app/features/my_order/widgets/delete.dart';

class OrderrCardPendingCustomer extends StatelessWidget {
  final MyOrderModel order;
  const OrderrCardPendingCustomer(this.order, {super.key});

  @override
  Widget build(BuildContext context) {
    // final controller = Get.put(SuggestionsPageController());
    // final MyOrderPageController controller = Get.put(MyOrderPageController());
    // final controller = Get.find<MyOrderPageController>();
    final controller = Get.find<OrderPendingController>();

    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          border: Border.symmetric(
            vertical: BorderSide(
                color: Colors.grey.shade300, width: 5), // top & bottom
            horizontal: BorderSide(
                color: Colors.grey.shade300, width: 6), // left & right
          ),
          // اللون الخارجي (الرمادي)
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white, // اللون الداخلي للكارد
            borderRadius: BorderRadius.circular(16), // منحني داخلي
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// شريط العنوان: ID + الوقت + 3 نقاط
              Row(
                children: [
                  // شوفي هي كيف

                  Text(
                    // "iddd",
                    "ID  ${order.id}",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                  const Spacer(),
                  Text(
                    "date ",
                    // formatDate(suggestion.timestamp), // فقط اليوم والشهر والسنة
                    style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                  ),

                  // Text(
                  //   "${suggestion.timestamp}",
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: Colors.grey.shade600,
                  //   ),
                  // ),
                  const SizedBox(width: 3),
                  IconButton(
                    onPressed: () {
                      // controller.deleteOrder(order.id);
                      AppDialogs.showDeleteConfirmation(
                        context: context,
                        onDelete: () async {
                          await controller.deleteOrder(order.id, context);
                        },
                      );
                    },
                    icon: Icon(Icons.more_horiz),
                    color: Colors.grey,
                    iconSize: 18,
                  ),
                ],
              ),
              const SizedBox(
                  height: 3), // هون بكون المسافة بين العنوان واللي فوقا

              /// فئة الخدمة + الرمز
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      // color: Color.fromARGB(255, 116, 140, 169),
                      color: StyleRepo.deepBlue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.home_work_outlined,
                        color: Colors.white, size: 22),
                  ),
                  const SizedBox(width: 7),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        // "title",
                        "${order.mainCategoryTitle}",
                        // "${order.description.split(' ').take(4).join(' ') + (order.description.split(' ').length > 4 ? '...' : '')}",
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
                      Text(
                        // "user id",
                        "${order.subCategoryTitle}",
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  )
                ],
              ),
              const SizedBox(height: 8),
              Divider(color: Colors.grey.shade300),

              /// الوصف
              Text(
                // "descr",
                "${order.description}",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade900,
                ),
              ),
              const SizedBox(height: 12),
              Divider(color: Colors.grey.shade300),
              // const SizedBox(height: 6),
              // Spacer(),

              /// الموقع
              Row(
                children: [
                  const Icon(Icons.location_on, size: 22, color: Colors.grey),
                  Text("${order.location}"),
                  const SizedBox(width: 6),
                  Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.toNamed(Pages.view_offer.value,
                              arguments: order.id);
                          // controller.ViewOffer(order.id);
                        },
                        child: Text(
                          " view offer",
                          // "Public: ${suggestion.isPublic}",
                          style: TextStyle(
                            fontSize: 13.5,
                            color: Colors.grey.shade700,
                          ),
                        )),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
          ),
        ));
  }
}
