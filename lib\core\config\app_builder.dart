// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:renvo_app/core/config/role.dart';

// import '../routes/routes.dart';
// import '../services/rest_api/rest_api.dart';

// class AppBuilder extends GetxService {
//   GetStorage box = GetStorage("app");

//   late Role role;
//   // GeneralUser? user;
//   String? token;

//   loadData() async {
//     await box.initStorage;

//     if (!box.hasData("role")) {
//       setRole(Role.new_user);
//     } else {
//       role = Role.fromString(box.read("role"));
//     }

//     if (box.hasData("token")) {
//       token = box.read("token");
//     }
//   }

//   setRole(Role role) {
//     this.role = role;
//     box.write("role", role.name);
//   }

//   // setUserData(GeneralUser user){}

//   setToken(String? token) {
//     this.token = token;
//     Get.find<APIService>().setToken(token);
//     if (token != null) {
//       box.write("token", token);
//     } else {
//       box.remove("token");
//     }
//   }

//   logout() {
//     setRole(Role.unregistered);
//     setToken(null);
//     Get.find<APIService>().setToken(null);
//     Get.toNamed(Pages.login.value);
//   }

//   init() async {
//     await loadData();

//     Get.put(APIService(token: token));

//     if (role == Role.unregistered || role == Role.new_user) {
//       Get.toNamed(Pages.login.value);
//     } else {
//       Get.toNamed(Pages.home.value);
//     }
//   }
// }

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:renvo_app/core/config/role.dart';
import '../routes/routes.dart';
import '../services/rest_api/rest_api.dart';

class AppBuilder extends GetxService {
  GetStorage box = GetStorage("app");

  late Role role;
  // GeneralUser? user;
  String? token;

  // final RxBool isProvider = false.obs; // هل المستخدم لديه حساب بروفايدر؟
  final RxBool isProviderMode = false.obs; // هل هو الآن بوضع البروفايدر؟

  // ----------------- تحميل القيم من التخزين -----------------
  Future<void> loadData() async {
    await box.initStorage;
    isProviderMode.value = box.read("isProviderMode") ?? false;
    // تحميل الدور
    if (!box.hasData("role")) {
      setRole(Role.new_user);
    } else {
      role = Role.fromString(box.read("role"));
    }

    // تحميل التوكن
    if (box.hasData("token")) {
      token = box.read("token");
    }

    // تحميل حالة البروفايدر
    // isProvider.value = box.read("isProvider") ?? false;
  }

  // ----------------- ضبط الدور (user, provider, ...) -----------------
  setRole(Role role) {
    this.role = role;
    box.write("role", role.name);
  }

  // ----------------- ضبط التوكن -----------------
  setToken(String? token) {
    this.token = token;
    Get.find<APIService>().setToken(token);
    if (token != null) {
      box.write("token", token);
    } else {
      box.remove("token");
    }
  }

  // ----------------- تحديث حالة البروفايدر في التخزين والحالة -----------------
  // void setIsProvider(bool val) {
  //   isProvider.value = val;
  //   box.write("isProvider", val);
  // }

  void setProviderMode(bool mode) {
    isProviderMode.value = mode;
    box.write("isProviderMode", mode);
  }

  // void toggleProviderMode() {
  //   if (isProvider.value) {
  //     isProviderMode.value = !isProviderMode.value;
  //     box.write("isProviderMode", isProviderMode.value);
  //     Get.snackbar(
  //       'تم تغيير الوضع',
  //       isProviderMode.value
  //           ? 'تم التحويل إلى وضع البروفايدر'
  //           : 'تم التحويل إلى وضع المستخدم',
  //     );
  void toggleProviderMode() {
    isProviderMode.value = !isProviderMode.value;
    box.write("isProviderMode", isProviderMode.value);
    Get.snackbar(
      'تم تغيير الوضع',
      isProviderMode.value
          ? 'تم التحويل إلى وضع البروفايدر'
          : 'تم التحويل إلى وضع المستخدم',
    );
  }

  // ----------------- تسجيل خروج -----------------
  logout() {
    setRole(Role.unregistered);
    setToken(null);
    // setIsProvider(false);
    setProviderMode(false);
    Get.find<APIService>().setToken(null);
    Get.toNamed(Pages.login.value);
  }

  // ----------------- بدء التطبيق -----------------
  init() async {
    await loadData();

    Get.put(APIService(token: token));

    if (role == Role.unregistered || role == Role.new_user) {
      Get.toNamed(Pages.login.value);
    } else {
      Get.toNamed(Pages.home.value);
    }
  }

  // ----------------- حفظ بيانات المستخدم بعد تسجيل الدخول -----------------
  // void saveUserDataFromApi(Map<String, dynamic> data) {
  //   // التحقق من وجود بروفايدر
  //   bool isProviderAccount = false;

  //   if (data.containsKey("provider_id") && data["provider_id"] != null) {
  //     isProviderAccount = true;
  //   } else if (data.containsKey("provider") && data["provider"] != null) {
  //     isProviderAccount = true;
  //   }

  //   // setIsProvider(isProviderAccount);

  //   // إذا لم يعد عنده بروفايدر (حذف)، أرجعه لوضع المستخدم
  //   if (!isProviderAccount) {
  //     setProviderMode(false);
  //   }

  //   // حفظ التوكن إذا موجود
  //   if (data.containsKey('token')) {
  //     setToken(data['token']);
  //   }
  //   // يمكنك إضافة حفظ بيانات أخرى هنا إذا أردت
  // }
}
