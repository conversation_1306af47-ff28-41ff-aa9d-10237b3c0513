import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renva0/core/widgets/modern_toast.dart';

import '../../../core/config/app_builder.dart';
import '../../../core/config/role.dart';
import '../../../core/routes/routes.dart';
import '../../../core/services/rest_api/rest_api.dart';

class LoginPageController extends GetxController {
  // Get app builder
  AppBuilder appBuilder = Get.find();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Text controllers - simple approach
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();

  // Simple reactive variables
  var hidePassword = true.obs;
  var errorMessage = ''.obs;
  var isLoading = false.obs;

  // Syria only - no country selection needed
  final String countryCode = '+963';
  final String cleanCountryCode = '963';
  final String countryName = 'Syria';

  @override
  void onInit() {
    super.onInit();

    phoneController.text = '123456789'; // 9 digits
    passwordController.text = '12345678';
  }

  @override
  void onClose() {
    phoneController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    String fullNumber = countryCode + value;

    return _validateSyrianNumber(fullNumber);
  }

  String? _validateSyrianNumber(String fullNumber) {
    // Remove +963 prefix for checking
    String numberPart = fullNumber.replaceFirst('+963', '');

    // Check if exactly 9 digits
    if (numberPart.length == 9 && RegExp(r'^\d{9}$').hasMatch(numberPart)) {
      return null;
    }

    // If not valid, provide helpful error
    return 'Please enter a valid Syrian number (9 digits after +963)';
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  void togglePasswordVisibility() {
    hidePassword.value = !hidePassword.value;
  }

  void clearError() {
    if (errorMessage.value.isNotEmpty) {
      errorMessage.value = '';
    }
  }

  void onPhoneChanged(String value) => clearError();
  void onPasswordChanged(String value) => clearError();

  void login() async {
    errorMessage.value = '';

    if (!formKey.currentState!.validate()) {
      return;
    }

    if (phoneController.text.isEmpty) {
      errorMessage.value = 'Please enter a valid phone number';
      _showError('Please enter a valid phone number');
      return;
    }

    String? phoneValidation = validatePhone(phoneController.text);
    if (phoneValidation != null) {
      errorMessage.value = phoneValidation;
      _showError(phoneValidation);
      return;
    }

    isLoading.value = true;

    try {
      await _makeLoginAPICall();
    } catch (e) {
      errorMessage.value = 'Network error. Please check your connection and try again.';
    } finally {
      isLoading.value = false;
    }
  }

  // 🔥 SIMPLIFIED: Basic API call for login
  Future<void> _makeLoginAPICall() async {
    String cleanPhone = phoneController.text;

    Map<String, dynamic> jsonData = {
      'phone': cleanPhone,
      'dial_country_code': cleanCountryCode,
      'password': passwordController.text,
      'device_token': 'FLUTTER_APP',
    };

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        body: jsonData,
        copyHeader: {'Accept': 'application/json', 'Content-Type': 'application/json'},
      ),
    );

    if (response.success && response.data != null) {
      await _handleSuccessfulLogin(response, cleanPhone, cleanCountryCode);
    } else {
      _handleLoginError(response);
    }
  }

  Future<void> _handleSuccessfulLogin(
    ResponseModel response,
    String cleanPhone,
    String dialCode,
  ) async {
    try {
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        String? token = responseData['token'];
        String? phoneVerifiedAt = responseData['phone_verified_at'];
        int isCompleted = responseData['is_completed'] ?? 0;

        if (token != null) {
          appBuilder.setToken(token);
        }

        if (phoneVerifiedAt != null) {
          appBuilder.setVerified(true);
        }

        if (isCompleted == 1) {
          appBuilder.setProfileCompleted(true);
        }

        appBuilder.setRole(Role.user);

        if (phoneVerifiedAt == null) {
          _showSuccess('Please verify your phone number');
          _navigateToVerification(cleanPhone, dialCode);
        } else if (isCompleted != 1) {
          // User verified but needs to complete profile
          _showSuccess('Please complete your profile');

          Get.offAllNamed(Pages.complete_info.value);
        } else {
          // User is fully authenticated
          _showSuccess('Welcome back!');

          Get.offAllNamed(Pages.home.value);
        }

        return;
      }

      appBuilder.setRole(Role.new_user);
      appBuilder.setVerified(false);
      appBuilder.setToken(null);
      appBuilder.setProfileCompleted(false);

      _showSuccess('OTP Sent');
      _navigateToVerification(cleanPhone, dialCode);
    } catch (e) {
      print(' Error processing successful login: $e');
      errorMessage.value = 'Login succeeded but failed to process response. Please try again.';
    }
  }

  void _navigateToVerification(String cleanPhone, String dialCode) {
    Get.toNamed(
      Pages.verify.value,
      arguments: {
        'phoneNumber': '+$dialCode$cleanPhone',
        'cleanPhone': cleanPhone,
        'dialCode': dialCode,
        'fromLogin': true,
      },
    );
  }

  void _handleLoginError(ResponseModel response) {
    String errorMsg = 'Login failed. Please check your credentials and try again.';

    try {
      if (response.data is Map<String, dynamic>) {
        final errorData = response.data as Map<String, dynamic>;

        if (errorData['message'] != null) {
          errorMsg = errorData['message'].toString();
        } else if (errorData['error'] != null) {
          errorMsg = errorData['error'].toString();
        } else if (errorData['errors'] != null) {
          if (errorData['errors'] is Map) {
            Map<String, dynamic> errors = errorData['errors'];
            List<String> errorMessages = [];
            errors.forEach((key, value) {
              if (value is List) {
                for (String error in value) {
                  errorMessages.add('$key: $error');
                }
              }
            });
            if (errorMessages.isNotEmpty) {
              errorMsg = errorMessages.join('\n');
            }
          }
        }
      }
    } catch (e) {
      print(' $e');
    }

    errorMessage.value = errorMsg;
    _showError(errorMsg);
  }

  void goToSignup() {
    Get.toNamed(Pages.signup.value);
  }

  void joinAsGuest() {
    appBuilder.setRole(Role.guest);
    appBuilder.setToken(null);
    appBuilder.setVerified(false);
    appBuilder.setProfileCompleted(false);
    Get.offAllNamed(Pages.home.value);
  }

  void forgotPassword() {
    _showInfo('Forgot password feature will be available soon');
  }

  void _showSuccess(String message) {
    PopUpToast.show(message);
    Get.snackbar('Success!', message, backgroundColor: Colors.green, colorText: Colors.white);
  }

  void _showError(String message) {
    PopUpToast.show(message);
    Get.snackbar('Error', message, backgroundColor: Colors.red, colorText: Colors.white);
  }

  void _showInfo(String message) {
    PopUpToast.show(message);
    Get.snackbar('Info', message, backgroundColor: Colors.blue, colorText: Colors.white);
  }
}
