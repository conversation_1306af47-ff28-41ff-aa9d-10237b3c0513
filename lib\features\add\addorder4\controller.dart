import 'dart:io';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/material.dart';

class AddOrderPage4Controller extends GetxController {
  TextEditingController descController = TextEditingController();
  RxList<XFile> images = <XFile>[].obs;
  Rx<RangeValues> priceRange =
      RangeValues(100, 140).obs; // القيم الافتراضية أو حسب المطلوب

  Future<void> pickImages() async {
    final picker = ImagePicker();
    final picked = await picker.pickMultiImage();
    if (picked != null && picked.isNotEmpty) {
      images.addAll(picked);
    }
  }
}
