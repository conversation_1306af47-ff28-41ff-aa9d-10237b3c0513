class ViewOfferModel {
  final int id;
  final int id_p;
  final String providerName;
  final String providerDescription;
  final String description;
  final String price;

  ViewOfferModel.ViewOfferModel({
    required this.id_p,
    required this.id,
    required this.providerName,
    required this.providerDescription,
    required this.description,
    required this.price,
  });

  factory ViewOfferModel.fromJson(Map<String, dynamic> json) {
    return ViewOfferModel.ViewOfferModel(
      id_p: json['provider']?['id'] ?? 0,
      id: json['id'] ?? 0,
      providerName: json['provider']?['name'] ?? '',
      providerDescription: json['provider']?['description'] ?? '',
      description: json['description'] ?? '',
      price: json['price'] ?? '',
    );
  }
}
