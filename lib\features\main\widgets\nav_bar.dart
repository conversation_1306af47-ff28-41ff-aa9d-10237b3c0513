import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/style/repo.dart';

import 'package:renvo_app/core/widgets/svg_icon.dart';

import 'package:renvo_app/gen/assets.gen.dart';

import '../controller.dart';

class NavBar extends StatelessWidget {
  const NavBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainPageController>();
    // MainPageController controller = Get.find();
    return Obx(
      () => NavigationBar(
        backgroundColor: Colors.white,
        onDestinationSelected: (page) => controller.currentPage.value = page,
        selectedIndex: controller.currentPage.value,
        destinations: [
          NavigationDestination(
            icon: SvgIcon(
              icon: Assets.icons.mainIcon,
              size: 30,
            ),
            label: "home",
          ),
          NavigationDestination(
            icon: SvgIcon(
              icon: Assets.icons.order,
              size: 30,
            ),
            label: "My Order",
          ),
          NavigationDestination(
            icon: SvgIcon(
              icon: Assets.icons.add,
              size: 25,
            ),
            label: "Add",
          ),
          NavigationDestination(
              icon: SvgIcon(
                icon: Assets.icons.chats,
                size: 30,
              ),
              label: "Chat"),
          NavigationDestination(
            icon: SvgIcon(
              icon: Assets.icons.profileImg,
              size: 30,
            ),
            label: "Profile",
          ),
        ],
      ),
    );
  }
}
