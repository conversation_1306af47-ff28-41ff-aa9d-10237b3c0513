import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/features/my_order/customer/OrderPending/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';
import 'package:renvo_app/features/my_order/widgets/order_card_pending_customer.dart';
import 'package:renvo_app/core/constants/controllers_tags.dart';
// import 'package:renvo_app/core/models/product/product.dart';
import 'package:renvo_app/core/services/pagination/options/list_view.dart';

import 'controller.dart';

class OrderCancelProPage extends StatelessWidget {
  const OrderCancelProPage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OrderCancelProController());

    return Scaffold(
      // appBar: AppBar(),
      body: ListViewPagination.separated(
        //
        tag: ControllersTags.orders_pager_cancel_provider,
        fetchApi: controller.fetchData,
        fromJson: MyOrderModel.fromJson,
        //
        initialLoading: Placeholder(),
        errorWidget: (error) => Text(error),
        onControllerInit: (pagerController) =>
            controller.pagerController = pagerController,

        separatorBuilder: (_, __) => SizedBox(height: 16),
        itemBuilder: (context, index, order //
            ) {
          return OrderrCardPendingCustomer(order);
          //  SizedBox(
          // height: 300,
          // child: Center(
          //text(controller.products[index].name)هاد الحكي كنا نكتبو بالليست فيو العادية
          // child: Text(order.description),
          // ),
          // );
        },
      ),
    );
    // Obx(() {
    //   if (controller.orders.isEmpty) {
    //     return const Center(child: CircularProgressIndicator());
    //   }
    //   return ListView.builder(
    //     itemCount: controller.orders.length,
    //     itemBuilder: (_, i) => OrderrCard(order: controller.orders[i]),
    //   );
    // });
  }
}
