import 'dart:ui';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:renvo_app/core/config/app_builder.dart';
import 'package:renvo_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/features/profile/controller.dart';
import 'package:renvo_app/gen/assets.gen.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProfilePageController());
    final appBuilder = Get.find<AppBuilder>();

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header Section with Profile Image and Info
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                child: ObsVariableBuilder(
                  obs: controller.profile,
                  builder: (context, profile) {
                    String displayName = "USER NAME";
                    String phoneNumber = "+999 123 456 789";

                    if (profile != null) {
                      if (appBuilder.isProviderMode.value &&
                          profile.provider != null) {
                        displayName = profile.provider?.name ?? "USER NAME";
                      } else {
                        displayName =
                            "${profile.firstName} ${profile.lastName}".trim();
                        if (displayName.isEmpty) displayName = "USER NAME";
                      }
                      phoneNumber = profile.phone ?? "+999 123 456 789";
                    }

                    return Column(
                      children: [
                        // Profile Image with Blur Effect
                        Stack(
                          // fit: StackFit.expand,
                          children: [
                            // ImageFiltered(
                            //   imageFilter:
                            //       ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                            //   child: Container(
                            //     width: double.infinity,
                            //     height: 200,
                            //     child: ClipOval(
                            //       child: Icon(
                            //         Icons.person,
                            //         color: Colors.white,
                            //         // size: 100,
                            //       ),
                            //     ),
                            //   ),
                            // ),
                            Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.grey[300]!, width: 3),
                              ),
                              child: ClipOval(
                                child: _buildDefaultAvatar(),
                              ),
                            ),
                            // Camera Icon
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                      color: Colors.grey[300]!, width: 2),
                                ),
                                child: const Icon(
                                  Icons.camera_alt,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // User Name
                        Text(
                          displayName,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),

                        // Phone Number
                        Text(
                          phoneNumber,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Edit Profile Button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Edit Profile",
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.info_outline,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Orders and Points Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildStatColumn(
                                "${profile.ordersCnt ?? 0}", "Orders"),
                            Container(
                              width: 1,
                              height: 40,
                              color: Colors.grey[300],
                            ),
                            _buildStatColumn("0", "Points"),
                          ],
                        ),
                      ],
                    );
                  },
                ),
              ),

              // Login as Service Provider Button
              ObsVariableBuilder(
                obs: controller.profile,
                builder: (context, profile) {
                  if (profile.provider != null) {
                    return Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 16),
                      child: ElevatedButton(
                        onPressed: appBuilder.toggleProviderMode,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4CAF50),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.swap_horiz, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              appBuilder.isProviderMode.value
                                  ? "Switch to User"
                                  : "Login as a service provider",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Loyalty Points and Payment Section
              Container(
                // margin: const EdgeInsets.all(20),
                // padding: const EdgeInsets.all(20),
                // decoration: BoxDecoration(
                //   color: Colors.white,
                //   borderRadius: BorderRadius.circular(16),
                //   boxShadow: [
                //     BoxShadow(
                //       color: Colors.black.withValues(alpha: 0.1),
                //       blurRadius: 10,
                //       offset: const Offset(0, 2),
                //     ),
                //   ],
                // )
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.all(10),
                      child: Container(
                        padding: EdgeInsets.all(10),
                        color: StyleRepo.gg,
                        child: Expanded(
                          child: Column(
                            children: [
                              const Text(
                                "Loyalty Points",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Placeholder image for loyalty points
                              Container(
                                width: 150,
                                height: 150,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: AppImage(
                                    path: Assets.images.loylte.path,
                                    type: ImageType.Asset),
                                //  const Icon(
                                //   Icons.stars,
                                //   color: Colors.orange,
                                //   size: 30,
                                // ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 40),
                    Container(
                      padding: EdgeInsets.all(10),
                      color: StyleRepo.gg,
                      child: Expanded(
                        child: Column(
                          children: [
                            const Text(
                              "Payment",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Placeholder image for payment
                            Container(
                                width: 150,
                                height: 150,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: AppImage(
                                    path: Assets.images.paymanet.path,
                                    type: ImageType.Asset)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Account Section
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Account",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildAccountItem(Icons.location_on, "My Location"),
                    _buildAccountItem(Icons.lock, "Change Password"),
                    _buildAccountItem(Icons.privacy_tip, "Privacy Policy"),
                    _buildAccountItem(Icons.support_agent, "Contact Us"),
                    _buildAccountItem(Icons.logout, "Logout",
                        color: Colors.red,
                        onTap: () => Get.find<AppBuilder>().logout()),
                  ],
                ),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [Colors.blue.shade200, Colors.purple.shade200],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withValues(alpha: 0.3),
        ),
        child: const Icon(
          Icons.person,
          color: Colors.white,
          size: 40,
        ),
      ),
    );
  }

  Widget _buildStatColumn(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildAccountItem(IconData icon, String title,
      {Color? color, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: color ?? Colors.grey[600],
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: color ?? Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (onTap == null)
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
          ],
        ),
      ),
    );
  }
}
