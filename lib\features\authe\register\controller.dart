import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/config/app_builder.dart';
import '../../../core/config/role.dart';
import '../../../core/routes/routes.dart';
import '../../../core/services/rest_api/rest_api.dart';

class RegisterController extends GetxController {
  final formKey = GlobalKey<FormState>();

  final phoneController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  var hidePassword = true.obs;
  var hideConfirmPassword = true.obs;
  var errorMessage = ''.obs;
  var isLoading = false.obs;

  final String countryCode = '+963';
  final String cleanCountryCode = '963';
  final String countryName = 'Syria';

  @override
  void onInit() {
    super.onInit();

    phoneController.text = '0912345678';
    passwordController.text = '12345678';
    confirmPasswordController.text = '12345678';
  }

  @override
  void onClose() {
    phoneController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    String fullNumber = countryCode + value;

    return _validateSyrianNumber(fullNumber);
  }

  String? _validateSyrianNumber(String fullNumber) {
    String numberPart = fullNumber.replaceFirst('+963', '');

    // Check if exactly 10 digits
    if (numberPart.length == 10 && RegExp(r'^\\d{10}$').hasMatch(numberPart)) {
      return null;
    }

    return 'Please enter a valid Syrian number (10 digits after +963)';
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  void togglePasswordVisibility() {
    hidePassword.value = !hidePassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    hideConfirmPassword.value = !hideConfirmPassword.value;
  }

  void clearError() {
    if (errorMessage.value.isNotEmpty) {
      errorMessage.value = '';
    }
  }

  void onPhoneChanged(String value) => clearError();
  void onPasswordChanged(String value) => clearError();
  void onConfirmPasswordChanged(String value) => clearError();
  void signup() async {
    errorMessage.value = '';

    if (!formKey.currentState!.validate()) {
      return;
    }

    if (phoneController.text.isEmpty) {
      errorMessage.value = 'Please enter a valid phone number';
      return;
    }

    isLoading.value = true;

    try {
      await _makeRegistrationAPICall();
    } catch (e) {
      errorMessage.value = 'Network error. Check your internet connection and try again';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _makeRegistrationAPICall() async {
    String cleanPhone = phoneController.text;

    Map<String, dynamic> jsonData = {
      'phone': cleanPhone,
      'dial_country_code': cleanCountryCode,
      'password': passwordController.text,
      'password_confirmation': confirmPasswordController.text,
    };

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        body: jsonData,
        copyHeader: {'Accept': 'application/json', 'Content-Type': 'application/json'},
      ),
    );

    if (response.success) {
      await _handleSuccessfulRegistration(response, cleanPhone, cleanCountryCode);
    } else {
      _handleRegistrationError(response);
    }
  }

  Future<void> _handleSuccessfulRegistration(
    ResponseModel response,
    String cleanPhone,
    String dialCode,
  ) async {
    final appBuilder = Get.find<AppBuilder>();

    appBuilder.setRole(Role.new_user);
    appBuilder.setToken(null);
    appBuilder.setVerified(false);
    appBuilder.setProfileCompleted(false);

    // Extract success message (same logic as original)
    String successMessage = 'Registration successful! Please verify your phone.';
    try {
      if (response.data is List && (response.data as List).isNotEmpty) {
        successMessage = (response.data as List).first.toString();
      } else if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData['message'] != null) {
          successMessage = responseData['message'].toString();
        }
      }
    } catch (e) {
      print(e);
    }

    // Show success message (same as original)
    Get.snackbar('Success!', 'OTP Sent', backgroundColor: Colors.green, colorText: Colors.white);
    _navigateToVerification(cleanPhone, dialCode);
  }

  void _navigateToVerification(String cleanPhone, String dialCode) {
    Get.toNamed(
      Pages.verify.value,
      arguments: {
        'phoneNumber': '+$dialCode$cleanPhone',
        'cleanPhone': cleanPhone,
        'dialCode': dialCode,
        'fromRegistration': true,
      },
    );
  }

  void _handleRegistrationError(ResponseModel response) {
    String errorMsg = 'Registration failed. Please check your information and try again.';

    // Extract error message from response (same logic as original)
    try {
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        if (responseData['message'] != null) {
          errorMsg = responseData['message'].toString();
        } else if (responseData['error'] != null) {
          errorMsg = responseData['error'].toString();
        }
      }
    } catch (e) {
      print('  $e');
    }
  }

  void goToLogin() {
    Get.toNamed(Pages.login.value);
  }

  bool get canSubmit {
    return phoneController.text.isNotEmpty &&
        passwordController.text.isNotEmpty &&
        confirmPasswordController.text.isNotEmpty &&
        !isLoading.value;
  }
}
