import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/core/widgets/svg_icon.dart';
import 'package:renvo_app/gen/assets.gen.dart';

import 'controller.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SplashScreenController());
    return Scaffold(
      body: Center(
          child: AppImage(
              path: Assets.images.logoBlueImg.path, type: ImageType.Asset)),
    );
  }
}
