import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renva0/features/auth/register/controller.dart';

import '../../../core/constants/controllers_tags.dart';
import '../../../core/localization/strings.dart';
import '../../../core/routes/routes.dart';
import '../../../core/style/repo.dart';
import '../../../core/widgets/auth_container.dart';
import '../../../gen/assets.gen.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RegisterController(), tag: ControllersTags.registerController);

    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: [StyleRepo.deepBlue, Color(0xff0048D9)],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap:
                          () => Get.dialog(
                            AlertDialog(
                              title: Text(
                                tr(LocaleKeys.dialogs_go_back_title),
                                style: theme.textTheme.titleMedium,
                              ),
                              content: Text(
                                tr(LocaleKeys.profile_profile_not_saved),
                                style: theme.textTheme.bodyMedium,
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Get.back(),
                                  child: Text(
                                    tr(LocaleKeys.common_cancel),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Get.back();
                                    Get.offAllNamed(Pages.login.value);
                                  },
                                  child: Text(
                                    tr(LocaleKeys.common_go_back),
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      child: Assets.icons.arrows.leftCircle.svg(
                        width: 24,
                        height: 24,
                        colorFilter: const ColorFilter.mode(StyleRepo.softWhite, BlendMode.srcIn),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      tr(LocaleKeys.common_back),
                      style: theme.textTheme.titleSmall?.copyWith(color: StyleRepo.softWhite),
                    ),
                  ],
                ),
              ),

              SizedBox(
                height: size.height * 0.18,
                child: Center(
                  child: Assets.images.logo.logo.svg(
                    width: 90,
                    height: 90,
                    colorFilter: const ColorFilter.mode(StyleRepo.softWhite, BlendMode.srcIn),
                  ),
                ),
              ),

              Expanded(
                child: AuthContainer(
                  showWatermark: true,
                  addScrolling: true,
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        Text(
                          tr(LocaleKeys.auth_signup_title),
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        const SizedBox(height: 8),

                        Text(
                          tr(LocaleKeys.auth_signup_subtitle),
                          style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                        ),

                        const SizedBox(height: 30),

                        _buildPhoneInput(context, controller, theme),

                        const SizedBox(height: 20),

                        Obx(
                          () => _buildFormField(
                            context: context,
                            label: tr(LocaleKeys.auth_password),
                            controller: controller.passwordController,
                            validator: controller.validatePassword,
                            hintText: tr(LocaleKeys.auth_add_strong_password),
                            prefixIcon: Assets.icons.document.keyhole.svg(
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(Colors.grey[600]!, BlendMode.srcIn),
                            ),
                            obscureText: controller.hidePassword.value,
                            suffixIcon: IconButton(
                              icon:
                                  controller.hidePassword.value
                                      ? Assets.icons.essentials.eyeOff.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      )
                                      : Assets.icons.essentials.eyeOn.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                              onPressed: controller.togglePasswordVisibility,
                            ),
                            onChanged: controller.onPasswordChanged,
                          ),
                        ),

                        const SizedBox(height: 20),

                        Obx(
                          () => _buildFormField(
                            context: context,
                            label: tr(LocaleKeys.auth_confirm_password),
                            controller: controller.confirmPasswordController,
                            validator: controller.validateConfirmPassword,
                            hintText: tr(LocaleKeys.auth_confirm_password_placeholder),
                            prefixIcon: Assets.icons.document.keyhole.svg(
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(Colors.grey[600]!, BlendMode.srcIn),
                            ),
                            obscureText: controller.hideConfirmPassword.value,
                            suffixIcon: IconButton(
                              icon:
                                  controller.hideConfirmPassword.value
                                      ? Assets.icons.essentials.eyeOff.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      )
                                      : Assets.icons.essentials.eyeOn.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                              onPressed: controller.toggleConfirmPasswordVisibility,
                            ),
                            onChanged: controller.onConfirmPasswordChanged,
                          ),
                        ),

                        const SizedBox(height: 40),

                        Obx(
                          () => SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: controller.isLoading.value ? null : controller.signup,
                              child:
                                  controller.isLoading.value
                                      ? Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              color: StyleRepo.softWhite,
                                              strokeWidth: 2,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(tr(LocaleKeys.common_loading)),
                                        ],
                                      )
                                      : Text(tr(LocaleKeys.common_confirm)),
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneInput(BuildContext context, RegisterController controller, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tr(LocaleKeys.auth_phone_number),
          style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: StyleRepo.softWhite,
            border: Border.all(color: StyleRepo.softGrey),
          ),
          child: Row(
            children: [
              Container(
                width: 100,
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  border: Border(right: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
                ),
                child: Row(
                  children: [
                    const Text('🇸🇾', style: TextStyle(fontSize: 18)),
                    const SizedBox(width: 8),
                    Text(
                      '+963',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: StyleRepo.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: TextFormField(
                  controller: controller.phoneController,
                  keyboardType: const TextInputType.numberWithOptions(
                    signed: false,
                    decimal: false,
                  ),
                  onChanged: controller.onPhoneChanged,
                  validator: controller.validatePhone,
                  style: theme.textTheme.bodyMedium,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.auth_phone_placeholder),
                    hintStyle: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildFormField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required String? Function(String?)? validator,
    required String hintText,
    required Widget prefixIcon,
    bool obscureText = false,
    TextInputType keyboardType = TextInputType.text,
    Widget? suffixIcon,
    Function(String)? onChanged,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          obscureText: obscureText,
          keyboardType: keyboardType,
          onChanged: onChanged,
          style: theme.textTheme.bodyMedium,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
            prefixIcon: Container(
              width: 60,
              height: 48,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                border: Border(right: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
              ),
              child: prefixIcon,
            ),
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: StyleRepo.softWhite,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          ),
        ),
      ],
    );
  }
}
