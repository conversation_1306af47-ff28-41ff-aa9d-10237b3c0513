import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData;
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderProcessingController extends GetxController {
  // var orders = <MyOrderModel>[].obs;

  // @override
  // void onInit() {
  //   super.onInit();
  //   fetchPendingOrders();
  // }

  // void fetchPendingOrders() async {
  //   await Future.delayed(Duration(seconds: 1)); // simulation
  //   orders.value = List.generate(
  //     5,
  //     (i) => MyOrderModel(
  //         id: i, title: "Pending Order ${i + 1}", status: OrderStatus.pending),
  //   );
  // }

  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.provider_orders,
        params: {
          "page": page, "status": "processing"
          // "Processing"
        },
        cancelToken: cancel,
      ),
    );
    return response;
  }

  Future<void> endOrder(int orderId) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.end_order,
        method: RequestMethod.Post,
        body: FormData.fromMap({
          "order_id": orderId,
        }),
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );

    if (response.success) {
      // pending_controller.pagerController.refreshData();

      // progress_controller.pagerController.refreshData();

      Get.snackbar("Success", "order complete successfully");
    } else {
      Get.snackbar("Error", "Failed to accept offer");
    }
    Get.back();
    // return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
