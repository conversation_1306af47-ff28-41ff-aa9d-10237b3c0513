class VOfferModel {
  final int id;
  final String description;
  final String price;
  final String time;
  final String timeType;
  final String status;
  final String providerName;
  final String providerAvatar;
  final double providerRate;

  VOfferModel({
    required this.id,
    required this.description,
    required this.price,
    required this.time,
    required this.timeType,
    required this.status,
    required this.providerName,
    required this.providerAvatar,
    required this.providerRate,
  });

  factory VOfferModel.fromJson(Map<String, dynamic> json) {
    return VOfferModel(
      id: json['id'] ?? 0,
      description: json['description'] ?? '',
      price: json['price'] ?? '0',
      time: json['time'] ?? '',
      timeType: json['time_type'] ?? '',
      status: json['status'] ?? '',
      providerName: json['provider']?['name'] ?? 'Unknown',
      providerAvatar: json['provider']?['avatar']?['small_url'] ?? '',
      providerRate: (json['provider']?['rate'] ?? 0).toDouble(),
    );
  }

  // static List<VOfferModel> listFromJson(List<dynamic> jsonList) {
  //   return jsonList.map((json) => VOfferModel.fromJson(json)).toList();
  // }
}
