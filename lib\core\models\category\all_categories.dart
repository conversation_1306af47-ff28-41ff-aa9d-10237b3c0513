class AllCategory {
  final int id;
  final String title;
  final int providerCount;
  final List<String> keywords;
  final String imageUrl;
  final String svg;

  AllCategory({
    required this.id,
    required this.title,
    required this.providerCount,
    required this.keywords,
    required this.imageUrl,
    required this.svg,
  });

  factory AllCategory.fromJson(Map<String, dynamic> json) {
    print(json);
    return AllCategory(
      id: json['id'],
      title: json['title'],
      providerCount: json['prv_cnt'],
      keywords: (json['keywords'] as List<dynamic>?)
              ?.map((k) => k['title'].toString())
              .toList() ??
          [],
      imageUrl: json['banner']?['original_url'] ?? '',
      svg: json['svg'],
    );
  }
}
