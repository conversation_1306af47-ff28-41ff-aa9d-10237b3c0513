import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';

import '../../../core/config/app_builder.dart';
import '../../../core/config/role.dart';
import '../../../core/routes/routes.dart';
import '../../../core/services/rest_api/rest_api.dart';
import '../../../core/widgets/modern_toast.dart';

class CompleteInfoController extends GetxController {
  final formKey = GlobalKey<FormState>();

  // Form controllers
  final fullName = TextEditingController();
  final lastName = TextEditingController();
  final email = TextEditingController();
  final nationalNumber = TextEditingController();
  final gender = TextEditingController();

  final profileImage = ''.obs;
  final idImage = ''.obs;
  final errorMessage = ''.obs;
  final isLoading = false.obs;

  AppBuilder get appBuilder => Get.find<AppBuilder>();

  @override
  void onClose() {
    fullName.dispose();
    lastName.dispose();
    email.dispose();
    nationalNumber.dispose();
    gender.dispose();
    super.onClose();
  }

  void completeProfile() async {
    // Clear any previous errors
    errorMessage.value = '';

    if (!formKey.currentState!.validate()) {
      return;
    }

    if (profileImage.value.isEmpty) {
      errorMessage.value = 'Profile photo is required';
      _showError('Profile photo is required');
      return;
    }

    if (idImage.value.isEmpty) {
      errorMessage.value = 'ID photo is required';
      _showError('ID photo is required');
      return;
    }

    if (fullName.text.trim().isEmpty ||
        lastName.text.trim().isEmpty ||
        email.text.trim().isEmpty ||
        nationalNumber.text.trim().isEmpty ||
        gender.text.trim().isEmpty) {
      errorMessage.value = 'Please fill all required fields';
      _showError('Please fill all required fields');
      return;
    }

    isLoading.value = true;

    try {
      // Check authentication token directly
      if (appBuilder.token == null || appBuilder.token!.isEmpty) {
        errorMessage.value = 'No authentication token found. Please login again.';
        _showError('No authentication token found. Please login again.');
        return;
      }

      await _makeProfileCompletionAPI();

      errorMessage.value = 'Failed to complete profile. Please try again.';
      _showError('Failed to complete profile. Please try again.');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _makeProfileCompletionAPI() async {
    try {
      FormData formData = FormData.fromMap({
        'first_name': fullName.text.trim(),
        'last_name': lastName.text.trim(),
        'nationalID': nationalNumber.text.trim(),
        'email': email.text.trim(),
        'gender_id': _getGenderId(gender.text.trim()),
      });

      if (profileImage.value.isNotEmpty) {
        formData.files.add(
          MapEntry(
            'profile_image',
            await MultipartFile.fromFile(profileImage.value, filename: 'profile_image.jpg'),
          ),
        );
      }

      if (idImage.value.isNotEmpty) {
        formData.files.add(
          MapEntry(
            'id_image',
            await MultipartFile.fromFile(idImage.value, filename: 'id_image.jpg'),
          ),
        );
      }

      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.updateProfile,
          method: RequestMethod.Post,
          body: formData,
          copyHeader: {'Accept': 'application/json'},
        ),
      );

      if (response.success) {
        _handleSuccess(response);
      } else {
        _handleError(response);
      }
    } catch (e) {
      throw Exception('API call failed: $e');
    }
  }

  String _getGenderId(String genderText) {
    final normalizedGender = genderText.toLowerCase().trim();

    if (normalizedGender == 'male' || normalizedGender == '1') {
      return '1';
    } else if (normalizedGender == 'female' || normalizedGender == '2') {
      return '2';
    }
    return '1';
  }

  void _handleSuccess(ResponseModel response) {
    try {
      final appBuilder = Get.find<AppBuilder>();

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        String? token = responseData['token'];
        String? phoneVerifiedAt = responseData['phone_verified_at'];
        int isCompleted = responseData['is_completed'] ?? 0;

        if (token != null) {
          appBuilder.setToken(token);
        }

        if (phoneVerifiedAt != null) {
          appBuilder.setVerified(true);
        }

        if (isCompleted == 1) {
          appBuilder.setProfileCompleted(true);
          appBuilder.setRole(Role.user);
        }
      } else {
        appBuilder.setProfileCompleted(true);
        appBuilder.setVerified(true);
        appBuilder.setRole(Role.user);
      }

      _showSuccess('Profile completed successfully! Welcome to Renva!');

      Future.delayed(Duration(milliseconds: 1000), () {
        Get.offAllNamed(Pages.home.value);
      });
    } catch (e) {
      errorMessage.value = 'Profile completed but failed to process response.';
    }
  }

  void _handleError(ResponseModel response) {
    String errorMsg = 'Failed to complete profile. Please try again.';

    try {
      if (response.data is Map<String, dynamic>) {
        final errorData = response.data as Map<String, dynamic>;

        if (errorData['message'] != null) {
          errorMsg = errorData['message'].toString();
        } else if (errorData['error'] != null) {
          errorMsg = errorData['error'].toString();
        } else if (errorData['errors'] != null) {
          Map<String, dynamic> fieldErrors = errorData['errors'];
          List<String> errorMessages = [];

          fieldErrors.forEach((field, errors) {
            if (errors is List) {
              for (String error in errors) {
                errorMessages.add('$field: $error');
              }
            }
          });

          if (errorMessages.isNotEmpty) {
            errorMsg = errorMessages.join('\n');
          }
        }
      }
    } catch (e) {
      print(' Error parsing error response: $e');
    }

    errorMessage.value = errorMsg;
    _showError(errorMsg);
  }

  Future<void> pickProfilePhoto() async {
    await _pickImage(isProfilePhoto: true);
  }

  Future<void> pickIdPhoto() async {
    await _pickImage(isProfilePhoto: false);
  }

  Future<void> _pickImage({required bool isProfilePhoto}) async {
    try {
      final ImageSource? source = await Get.dialog<ImageSource>(
        AlertDialog(
          title: Text(
            isProfilePhoto ? 'Select Profile Photo' : 'Select ID Photo',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.camera_alt, color: Colors.blue),
                title: Text('Camera'),
                subtitle: Text('Take a new photo'),
                onTap: () => Get.back(result: ImageSource.camera),
              ),
              ListTile(
                leading: Icon(Icons.photo_library, color: Colors.green),
                title: Text('Gallery'),
                subtitle: Text('Choose from gallery'),
                onTap: () => Get.back(result: ImageSource.gallery),
              ),
            ],
          ),
          actions: [TextButton(onPressed: () => Get.back(), child: Text('Cancel'))],
        ),
      );

      if (source != null) {
        final ImagePicker picker = ImagePicker();
        final XFile? pickedImage = await picker.pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );

        if (pickedImage != null) {
          if (isProfilePhoto) {
            profileImage.value = pickedImage.path;
          } else {
            idImage.value = pickedImage.path;
          }

          if (errorMessage.value.isNotEmpty) {
            errorMessage.value = '';
          }

          _showSuccess('Image selected successfully!');
        }
      }
    } catch (e) {
      print(' Error picking image: $e');
      errorMessage.value = 'Failed to select image. Please try again.';
    }
  }

  String? validateFullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Full name is required';
    }
    if (value.trim().length < 2) {
      return 'Full name must be at least 2 characters';
    }
    return null;
  }

  String? validateLastName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Last name is required';
    }
    if (value.trim().length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w{2,}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validateNationalNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'National number is required';
    }
    if (value.trim().length < 5) {
      return 'National number must be at least 5 characters';
    }
    return null;
  }

  String? validateGender(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Gender is required';
    }

    final normalizedValue = value.trim().toLowerCase();
    if (['male', 'female', '1', '2'].contains(normalizedValue)) {
      return null;
    }

    return 'Please enter Male or Female';
  }

  void clearError() {
    if (errorMessage.value.isNotEmpty) {
      errorMessage.value = '';
    }
  }

  void _showSuccess(String message) {
    PopUpToast.show(message);
    Get.snackbar('Success!', message, backgroundColor: Colors.green, colorText: Colors.white);
  }

  void _showError(String message) {
    PopUpToast.show(message);
    Get.snackbar('Error', message, backgroundColor: Colors.red, colorText: Colors.white);
  }
}
