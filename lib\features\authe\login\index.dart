// Simplified Login Page UI - Junior Level (Syria Only, 10 Digits)
// File: features/auth/login/index.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/localization/strings.dart';
import '../../../core/style/repo.dart';
import '../../../core/widgets/auth_container.dart';
import '../../../gen/assets.gen.dart';
import 'controller.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LoginPageController(), tag: 'login_controller', permanent: true);

    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: [StyleRepo.deepBlue, Color(0xff0048D9)],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Logo section
              SizedBox(
                height: size.height * 0.15,
                child: Center(
                  child: Assets.images.logo.logo.svg(
                    width: 90,
                    height: 90,
                    colorFilter: const ColorFilter.mode(StyleRepo.softWhite, BlendMode.srcIn),
                  ),
                ),
              ),

              // Main content
              Expanded(
                child: AuthContainer(
                  showWatermark: true,
                  addScrolling: true,
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Welcome section
                        Row(
                          children: [
                            Text(
                              tr(LocaleKeys.common_welcome_to),
                              style: theme.textTheme.headlineSmall,
                            ),
                            const SizedBox(width: 10),
                            Assets.images.logo.renva.svg(
                              width: 20,
                              height: 20,
                              colorFilter: const ColorFilter.mode(
                                StyleRepo.deepBlue,
                                BlendMode.srcIn,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Login title
                        Row(
                          children: [
                            Text(
                              tr(LocaleKeys.auth_login_title),
                              style: theme.textTheme.headlineSmall,
                            ),
                            const SizedBox(width: 5),
                            Assets.icons.emojis.veryHappyFace.svg(
                              width: 20,
                              height: 20,
                              colorFilter: const ColorFilter.mode(
                                StyleRepo.forestGreen,
                                BlendMode.srcIn,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        Text(
                          tr(LocaleKeys.auth_login_subtitle),
                          style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                        ),

                        const SizedBox(height: 30),

                        // 🔥 SIMPLIFIED: Phone input (Syria only)
                        _buildSimplePhoneInput(context, controller, theme),

                        const SizedBox(height: 20),

                        // Password field
                        Obx(
                          () => _buildFormField(
                            context: context,
                            label: tr(LocaleKeys.auth_password),
                            controller: controller.passwordController,
                            validator: controller.validatePassword,
                            hintText: tr(LocaleKeys.auth_password_placeholder),
                            prefixIcon: Assets.icons.document.keyhole.svg(
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(Colors.grey[600]!, BlendMode.srcIn),
                            ),
                            obscureText: controller.hidePassword.value,
                            suffixIcon: IconButton(
                              icon:
                                  controller.hidePassword.value
                                      ? Assets.icons.essentials.eyeOff.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      )
                                      : Assets.icons.essentials.eyeOn.svg(
                                        width: 20,
                                        height: 20,
                                        colorFilter: const ColorFilter.mode(
                                          StyleRepo.grey,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                              onPressed: controller.togglePasswordVisibility,
                            ),
                            onChanged: controller.onPasswordChanged,
                          ),
                        ),

                        // Forgot password link
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: controller.forgotPassword,
                            child: Text(
                              tr(LocaleKeys.auth_forgot_password),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: StyleRepo.deepBlue,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),

                        // 🔥 SIMPLIFIED: Error message display
                        Obx(
                          () =>
                              controller.errorMessage.value.isNotEmpty
                                  ? Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    margin: const EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.red[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.red[200]!),
                                    ),
                                    child: Text(
                                      controller.errorMessage.value,
                                      style: theme.textTheme.bodyMedium?.copyWith(
                                        color: Colors.red[700],
                                      ),
                                    ),
                                  )
                                  : const SizedBox.shrink(),
                        ),

                        // Login button with loading state
                        Obx(
                          () => SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: controller.isLoading.value ? null : controller.login,
                              child:
                                  controller.isLoading.value
                                      ? Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              color: StyleRepo.softWhite,
                                              strokeWidth: 2,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text('Signing In...'),
                                        ],
                                      )
                                      : Text(
                                        tr(LocaleKeys.auth_login),
                                        style: theme.textTheme.titleSmall?.copyWith(
                                          color: StyleRepo.softWhite,
                                        ),
                                      ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Sign up redirect
                        Center(
                          child: TextButton(
                            onPressed: controller.goToSignup,
                            child: Text(
                              tr(LocaleKeys.auth_signup),
                              style: theme.textTheme.titleSmall?.copyWith(
                                color: StyleRepo.forestGreen,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 15),

                        // Join as guest
                        Center(
                          child: InkWell(
                            onTap: controller.joinAsGuest,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.login, size: 16, color: StyleRepo.deepBlue),
                                const SizedBox(width: 5),
                                Text(
                                  tr(LocaleKeys.auth_join_as_guest),
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    color: StyleRepo.deepBlue,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 🔥 SIMPLIFIED: Simple phone input (Syria only) - same as registration
  Widget _buildSimplePhoneInput(
    BuildContext context,
    LoginPageController controller,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tr(LocaleKeys.auth_phone_number),
          style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),

        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: StyleRepo.softGrey, width: 1),
          ),
          child: Row(
            children: [
              // Syria country section (fixed - no selection)
              Container(
                width: 100,
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  border: Border(right: BorderSide(color: StyleRepo.softGrey, width: 1)),
                ),
                child: Row(
                  children: [
                    const Text('🇸🇾', style: TextStyle(fontSize: 18)),
                    const SizedBox(width: 8),
                    Text(
                      '+963',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: StyleRepo.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Phone number input
              Expanded(
                child: TextFormField(
                  controller: controller.phoneController,
                  keyboardType: const TextInputType.numberWithOptions(
                    signed: false,
                    decimal: false,
                  ),
                  validator: controller.validatePhone,
                  style: theme.textTheme.bodyMedium,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.auth_phone_placeholder),
                    hintStyle: theme.textTheme.labelSmall?.copyWith(color: Colors.grey[400]),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Helper text for 10-digit requirement
        Text(
          'Enter 10 digits after +963',
          style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  // Helper method to build form fields (same as registration)
  Widget _buildFormField({
    required BuildContext context,
    required String label,
    required TextEditingController controller,
    required String? Function(String?)? validator,
    required String hintText,
    required Widget prefixIcon,
    bool obscureText = false,
    TextInputType keyboardType = TextInputType.text,
    Widget? suffixIcon,
    Function(String)? onChanged,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          obscureText: obscureText,
          keyboardType: keyboardType,
          onChanged: onChanged,
          style: theme.textTheme.bodyMedium,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[400]),
            prefixIcon: Container(
              width: 60,
              height: 48,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                border: Border(right: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
              ),
              child: prefixIcon,
            ),
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: StyleRepo.softWhite,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          ),
        ),
      ],
    );
  }
}
