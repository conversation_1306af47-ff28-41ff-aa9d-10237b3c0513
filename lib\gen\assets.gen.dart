/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/Vector.svg
  SvgGenImage get vector => const SvgGenImage('assets/icons/Vector.svg');

  /// File path: assets/icons/add.svg
  SvgGenImage get add => const SvgGenImage('assets/icons/add.svg');

  /// File path: assets/icons/cart.svg
  SvgGenImage get cart => const SvgGenImage('assets/icons/cart.svg');

  /// File path: assets/icons/chats.svg
  SvgGenImage get chats => const SvgGenImage('assets/icons/chats.svg');

  /// File path: assets/icons/explore.svg
  SvgGenImage get explore => const SvgGenImage('assets/icons/explore.svg');

  /// File path: assets/icons/heart.svg
  SvgGenImage get heart => const SvgGenImage('assets/icons/heart.svg');

  /// File path: assets/icons/home.svg
  SvgGenImage get home => const SvgGenImage('assets/icons/home.svg');

  /// File path: assets/icons/logo_addorder.svg
  SvgGenImage get logoAddorder =>
      const SvgGenImage('assets/icons/logo_addorder.svg');

  /// File path: assets/icons/logo_white.svg
  SvgGenImage get logoWhite => const SvgGenImage('assets/icons/logo_white.svg');

  /// File path: assets/icons/main_icon.svg
  SvgGenImage get mainIcon => const SvgGenImage('assets/icons/main_icon.svg');

  /// File path: assets/icons/order.svg
  SvgGenImage get order => const SvgGenImage('assets/icons/order.svg');

  /// File path: assets/icons/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/icons/profile.svg');

  /// File path: assets/icons/profile_img.svg
  SvgGenImage get profileImg =>
      const SvgGenImage('assets/icons/profile_img.svg');

  /// File path: assets/icons/renvo_back.svg
  SvgGenImage get renvoBack => const SvgGenImage('assets/icons/renvo_back.svg');

  /// File path: assets/icons/renvo_background.svg
  SvgGenImage get renvoBackground =>
      const SvgGenImage('assets/icons/renvo_background.svg');

  /// File path: assets/icons/svg logo.svg
  SvgGenImage get svgLogo => const SvgGenImage('assets/icons/svg logo.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    vector,
    add,
    cart,
    chats,
    explore,
    heart,
    home,
    logoAddorder,
    logoWhite,
    mainIcon,
    order,
    profile,
    profileImg,
    renvoBack,
    renvoBackground,
    svgLogo,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/-5947083705891800988_120.jpg
  AssetGenImage get a5947083705891800988120 =>
      const AssetGenImage('assets/images/-5947083705891800988_120.jpg');

  /// File path: assets/images/background.svg
  SvgGenImage get background =>
      const SvgGenImage('assets/images/background.svg');

  /// File path: assets/images/background_img.png
  AssetGenImage get backgroundImg =>
      const AssetGenImage('assets/images/background_img.png');

  /// File path: assets/images/delete_png.png
  AssetGenImage get deletePng =>
      const AssetGenImage('assets/images/delete_png.png');

  /// File path: assets/images/download_invisio.png
  AssetGenImage get downloadInvisio =>
      const AssetGenImage('assets/images/download_invisio.png');

  /// File path: assets/images/home_pro_back.png
  AssetGenImage get homeProBack =>
      const AssetGenImage('assets/images/home_pro_back.png');

  /// File path: assets/images/home_service.png
  AssetGenImage get homeService =>
      const AssetGenImage('assets/images/home_service.png');

  /// File path: assets/images/join_as_s.png
  AssetGenImage get joinAsS =>
      const AssetGenImage('assets/images/join_as_s.png');

  /// File path: assets/images/logo_blue.svg
  SvgGenImage get logoBlue => const SvgGenImage('assets/images/logo_blue.svg');

  /// File path: assets/images/logo_blue_img.png
  AssetGenImage get logoBlueImg =>
      const AssetGenImage('assets/images/logo_blue_img.png');

  /// File path: assets/images/logo_gptback.png
  AssetGenImage get logoGptback =>
      const AssetGenImage('assets/images/logo_gptback.png');

  /// File path: assets/images/logo_white.png
  AssetGenImage get logoWhite =>
      const AssetGenImage('assets/images/logo_white.png');

  /// File path: assets/images/loylte.png
  AssetGenImage get loylte => const AssetGenImage('assets/images/loylte.png');

  /// File path: assets/images/paymanet.png
  AssetGenImage get paymanet =>
      const AssetGenImage('assets/images/paymanet.png');

  /// File path: assets/images/renvo_back_png.png
  AssetGenImage get renvoBackPng =>
      const AssetGenImage('assets/images/renvo_back_png.png');

  /// File path: assets/images/service_service.png
  AssetGenImage get serviceService =>
      const AssetGenImage('assets/images/service_service.png');

  /// File path: assets/images/white_logo_lilas.jpg
  AssetGenImage get whiteLogoLilas =>
      const AssetGenImage('assets/images/white_logo_lilas.jpg');

  /// List of all assets
  List<dynamic> get values => [
    a5947083705891800988120,
    background,
    backgroundImg,
    deletePng,
    downloadInvisio,
    homeProBack,
    homeService,
    joinAsS,
    logoBlue,
    logoBlueImg,
    logoGptback,
    logoWhite,
    loylte,
    paymanet,
    renvoBackPng,
    serviceService,
    whiteLogoLilas,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, en];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
