import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/routes/routes.dart';
import 'package:renvo_app/core/style/repo.dart';
import 'package:renvo_app/core/widgets/image.dart';
import 'package:renvo_app/features/home/<USER>';
import 'package:renvo_app/features/home/<USER>/service-card.dart';
import 'package:renvo_app/features/home/<USER>/story_card.dart';
import 'package:renvo_app/gen/assets.gen.dart';

class UserHomeWidget extends StatelessWidget {
  const UserHomeWidget({
    super.key,
    required this.controller,
  });

  final HomePageController controller;

//   @override
//   Widget build(BuildContext context) {
//     return Stack(children: [
//       Container(
//         height: double.infinity,
//         child: AppImage(
//           path: Assets.images.backgroundImg.path,
//           type: ImageType.Asset,
//           fit: BoxFit.fill,
//         ),
//       ),
//       Row(
//         // crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Assets.icons.renvoBackground.svg(),
//           Assets.icons.vector.svg()
//         ],
//       ),
//       // SizedBox(
//       //   child: _buildServiceGrid(),
//       // ),
//       Positioned(
//           top: 50,
//           left: 50,
//           child: SizedBox(height: 300, width: 300, child: buildServiceGrid())),
//       Positioned(
//         bottom: 0,
//         left: 0,
//         right: 0,
//         top: MediaQuery.sizeOf(context).height * 0.30,
//         child: Container(
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.only(
//                 topLeft: Radius.circular(30), topRight: Radius.circular(30)),
//             color: StyleRepo.white,
//           ),
//           child: ListView(
//             // mainAxisAlignment: MainAxisAlignment.start,
//             // crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Padding(
//                 padding: EdgeInsets.only(left: 20, top: 20),
//                 child: Text(
//                   "Curated Stories",
//                   style: TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(
//                   left: 20,
//                 ),
//                 child: Text(
//                   "Discover New horizons",
//                   style: TextStyle(
//                     color: Colors.grey,
//                   ),
//                 ),
//               ),
//               Obx(
//                 () => SizedBox(
//                   height: 250,
//                   child: ListView.builder(
//                     scrollDirection: Axis.horizontal,
//                     itemCount: controller.stories.length,
//                     itemBuilder: (context, index) {
//                       final story = controller.stories[index];
//                       return StoryCard(story: story);
//                     },
//                   ),
//                 ),
//               ),
//               SizedBox(
//                 height: 10,
//               ),
//               TextButton(
//                   onPressed: () {
//                     Get.toNamed(Pages.Join_Provider.value);
//                   },
//                   child: Text("Join as a Services Providers"))
//             ],
//           ),
//         ),
//       ),
//     ]);
//   }
// }
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Stack(
      children: [
        Container(
          height: double.infinity,
          child: AppImage(
            path: Assets.images.backgroundImg.path,
            type: ImageType.Asset,
            fit: BoxFit.fill,
          ),
        ),
        Row(
          // crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Assets.icons.renvoBackground.svg(),
            Assets.icons.vector.svg()
          ],
        ),
        Positioned(
          top: -117,
          left: -262,
          right: 0,
          bottom: MediaQuery.of(context).size.height * 0.5,
          child: Opacity(
            opacity: 0.18,
          ),
        ),
        Column(
          children: [
            SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Padding(
                  //   padding: const EdgeInsets.fromLTRB(20, 5, 20, 0),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.center,
                  //     children: [
                  //       Assets.images.downloadInvisio
                  //           .image(width: 240, height: 75),
                  //     ],
                  //   ),
                  // ),
                  SizedBox(
                    height: 20,
                  )
                ],
              ),
            ),
            Expanded(
              child: MediaQuery(
                data: MediaQuery.of(
                  context,
                ).copyWith(padding: const EdgeInsets.only(top: 0, bottom: 0)),
                child: NestedScrollView(
                  headerSliverBuilder: (context, innerBoxIsScrolled) {
                    return [
                      SliverPadding(
                        padding: const EdgeInsets.fromLTRB(32, 8, 32, 16),
                        sliver: SliverToBoxAdapter(
                          child: SizedBox(
                              height: size.height *
                                  0.18, //  من هون منغير مقاسات الشاشة القسم الازرق والقسم الابيض
                              child:
                                  // StoryCard(story: story),
                                  buildServiceGrid()),
                        ),
                      ),
                    ];
                  },
                  body: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(30),
                      ),
                    ),
                    child: CustomScrollView(
                      physics: const BouncingScrollPhysics(),
                      slivers: [
                        SliverToBoxAdapter(
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.only(
                                top: 15,
                                bottom: 5,
                              ),
                              child: Container(
                                width: 40,
                                height: 4,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SliverToBoxAdapter(
                          child: Obx(
                            () => SizedBox(
                              height: 250,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: controller.stories.length,
                                itemBuilder: (context, index) {
                                  final story = controller.stories[index];
                                  return StoryCard(story: story);
                                },
                              ),
                            ),
                          ),

                          // _ StoryCard(story: story),//////////////////////////////////////////////////////////////////////////////
                        ),

                        // SliverToBoxAdapter(child: _buildJoinSection(context)),
                        SliverToBoxAdapter(
                            child: Column(children: [
                          SizedBox(height: 30),
                          InkWell(
                            onTap: () {
                              Get.toNamed(Pages.Join_Provider.value);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(30),
                                color: StyleRepo.deepBlueFegma,
                              ),
                              child: AppImage(
                                  path: Assets.images.joinAsS.path,
                                  type: ImageType.Asset),
                            ),
                          ),
                        ])
                            // TextButton(
                            //     onPressed: () {
                            //       Get.toNamed(Pages.Join_Provider.value);
                            //     },
                            //     child: Text("Join as a Services Providers")),
                            // )
                            //  SizedBox(height: 8)
                            ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
